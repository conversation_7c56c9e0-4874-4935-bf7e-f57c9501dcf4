"""
抽取服务
"""

import json
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any
from optimized_llm_extractor import OptimizedLLMExtractor
from models.database import db
from services.log_manager import log_manager


class ExtractionService:
    """抽取服务"""
    
    def __init__(self, max_workers: int = 8):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.extractor_pool = []
        self.pool_lock = threading.Lock()
    
    def get_extractor(self, max_iterations: int = 3) -> OptimizedLLMExtractor:
        """从池中获取抽取器实例"""
        with self.pool_lock:
            if self.extractor_pool:
                extractor = self.extractor_pool.pop()
                extractor.max_iterations = max_iterations
                return extractor
            else:
                return OptimizedLLMExtractor(max_iterations=max_iterations)
    
    def return_extractor(self, extractor: OptimizedLLMExtractor):
        """归还抽取器到池中"""
        with self.pool_lock:
            if len(self.extractor_pool) < 10:  # 限制池大小
                self.extractor_pool.append(extractor)
    
    def run_extraction_with_logs(
        self, 
        task_id: str, 
        clinical_text: str, 
        ecrf_info: str, 
        max_iterations: int
    ) -> Dict[str, Any]:
        """运行抽取任务（带日志记录）"""
        
        extractor = self.get_extractor(max_iterations)
        
        try:
            # 创建自定义抽取器，重写打印方法
            class LoggingExtractor(OptimizedLLMExtractor):
                def __init__(self, max_iterations: int, task_id: str):
                    super().__init__(max_iterations)
                    self.task_id = task_id
                
                def log_step(self, message: str):
                    """记录步骤日志"""
                    log_manager.add_log(self.task_id, message)
            
            # 使用带日志的抽取器
            logging_extractor = LoggingExtractor(max_iterations, task_id)
            
            # 手动执行抽取步骤并记录日志
            log_manager.add_log(task_id, "🧠 第1步：深度分析临床文本...")
            clinical_analysis = logging_extractor.text_analyzer(clinical_text=clinical_text)
            
            log_manager.add_log(task_id, "🏗️ 第2步：深度分析eCRF结构...")
            ecrf_analysis = logging_extractor.ecrf_analyzer(ecrf_info=ecrf_info)
            
            log_manager.add_log(task_id, "⚡ 第3步：智能生成检查规则...")
            rule_generation = logging_extractor.rule_generator(
                clinical_analysis=clinical_analysis.comprehensive_analysis,
                ecrf_analysis=ecrf_analysis.structure_analysis
            )
            
            log_manager.add_log(task_id, "✅ 第4步：迭代验证和优化...")
            current_rules = rule_generation.extracted_rules
            validation_history = []
            optimization_history = []
            
            for iteration in range(max_iterations):
                log_manager.add_log(task_id, f"   🔄 迭代 {iteration + 1}/{max_iterations}...")
                
                # 验证当前规则
                validation = logging_extractor.rule_validator(
                    original_text=clinical_text,
                    generated_rules=current_rules
                )
                validation_history.append(validation.validation_report)
                
                # 检查是否通过验证
                if "验证通过" in validation.validation_report:
                    log_manager.add_log(task_id, f"   ✅ 第{iteration + 1}次迭代通过验证！")
                    current_rules = validation.optimized_rules
                    break
                else:
                    log_manager.add_log(task_id, f"   ❌ 第{iteration + 1}次迭代未通过验证")
                    
                    # 如果未通过且不是最后一次迭代，进行优化
                    if iteration < max_iterations - 1:
                        log_manager.add_log(task_id, f"   🔧 发现问题，进行优化...")
                        optimization = logging_extractor.iterative_optimizer(
                            original_text=clinical_text,
                            current_rules=current_rules,
                            validation_issues=validation.validation_report,
                            ecrf_structure=ecrf_info
                        )
                        optimization_history.append(optimization.optimization_analysis)
                        current_rules = optimization.optimized_rules
                    else:
                        log_manager.add_log(task_id, f"   ⚠️ 达到最大迭代次数，使用当前最佳结果")
                        current_rules = validation.optimized_rules
            
            # 解析最终规则
            try:
                final_rules = json.loads(current_rules)
                if not isinstance(final_rules, list):
                    final_rules = [final_rules] if final_rules else []
            except json.JSONDecodeError as e:
                log_manager.add_log(task_id, f"⚠️ JSON解析失败: {e}")
                final_rules = []
            
            return {
                "clinical_analysis": clinical_analysis.comprehensive_analysis,
                "ecrf_analysis": ecrf_analysis.structure_analysis,
                "reasoning_process": rule_generation.reasoning_process,
                "validation_history": validation_history,
                "optimization_history": optimization_history,
                "iterations_used": iteration + 1 if 'iteration' in locals() else 0,
                "rules": final_rules,
                "total_rules": len(final_rules)
            }
            
        finally:
            self.return_extractor(extractor)
    
    async def process_extraction_async(
        self,
        task_id: str,
        clinical_text: str,
        ecrf_info: str,
        max_iterations: int
    ):
        """异步处理抽取任务"""
        
        try:
            # 更新任务状态为处理中
            db.update_task_status(task_id, "processing")
            log_manager.add_log(task_id, "📋 开始处理抽取任务...")
            
            # 在线程池中执行CPU密集型任务
            import asyncio
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self.run_extraction_with_logs,
                task_id,
                clinical_text,
                ecrf_info,
                max_iterations
            )
            
            log_manager.add_log(task_id, f"✅ 抽取完成！共生成 {result['total_rules']} 条规则")
            
            # 保存结果到数据库
            db.save_result(task_id, result)
            db.update_task_status(
                task_id, 
                "completed",
                iterations_used=result["iterations_used"],
                total_rules=result["total_rules"]
            )
            
            # 清理日志流
            log_manager.cleanup_stream(task_id)
            
        except Exception as e:
            log_manager.add_log(task_id, f"❌ 抽取失败: {str(e)}")
            
            # 更新错误状态
            db.update_task_status(task_id, "failed", error_message=str(e))
            
            # 清理日志流
            log_manager.cleanup_stream(task_id)


# 全局抽取服务
extraction_service = ExtractionService()
