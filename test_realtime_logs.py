"""
测试实时日志功能的客户端
"""

import requests
import time
import threading
from typing import Optional

class RealtimeLogClient:
    """实时日志客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def submit_task(self, clinical_text: str, ecrf_info: str, max_iterations: int = 3) -> str:
        """提交抽取任务"""
        response = self.session.post(f"{self.base_url}/extract", json={
            "clinical_text": clinical_text,
            "ecrf_info": ecrf_info,
            "max_iterations": max_iterations
        })
        
        if response.status_code == 200:
            return response.json()["task_id"]
        else:
            raise Exception(f"提交任务失败: {response.text}")
    
    def watch_logs(self, task_id: str, callback=None):
        """监听任务日志"""
        print(f"🔍 开始监听任务 {task_id} 的实时日志...")
        
        try:
            response = self.session.get(
                f"{self.base_url}/extract/{task_id}/logs",
                stream=True,
                headers={"Accept": "text/event-stream"}
            )
            
            if response.status_code != 200:
                print(f"❌ 获取日志失败: {response.text}")
                return
            
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith("data: "):
                    log_message = line[6:]  # 移除 "data: " 前缀
                    
                    if log_message.startswith("[TASK_COMPLETED]"):
                        print(f"🎉 {log_message}")
                        break
                    else:
                        print(f"📋 {log_message}")
                        
                        if callback:
                            callback(log_message)
        
        except Exception as e:
            print(f"❌ 监听日志时出错: {e}")
    
    def get_result(self, task_id: str) -> dict:
        """获取任务结果"""
        response = self.session.get(f"{self.base_url}/extract/{task_id}")
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"获取结果失败: {response.text}")
    
    def run_with_realtime_logs(self, clinical_text: str, ecrf_info: str, max_iterations: int = 3):
        """运行任务并实时显示日志"""
        
        print("🎯 提交抽取任务...")
        task_id = self.submit_task(clinical_text, ecrf_info, max_iterations)
        print(f"✅ 任务已提交，ID: {task_id}")
        
        # 启动日志监听线程
        log_thread = threading.Thread(target=self.watch_logs, args=(task_id,))
        log_thread.daemon = True
        log_thread.start()
        
        # 等待任务完成
        while True:
            result = self.get_result(task_id)
            
            if result["status"] == "completed":
                print(f"\n🎉 任务完成！")
                print(f"   规则数量: {result['total_rules']}")
                print(f"   迭代次数: {result['iterations_used']}")
                break
            elif result["status"] == "failed":
                print(f"\n❌ 任务失败: {result.get('error', '未知错误')}")
                break
            
            time.sleep(2)
        
        # 等待日志线程结束
        log_thread.join(timeout=5)
        
        return result


def test_simple_case():
    """测试简单案例"""
    
    print("🧪 测试简单案例的实时日志")
    print("=" * 60)
    
    client = RealtimeLogClient()
    
    clinical_text = """入选标准:
年龄18~55周岁，男女均可；
男性体重≥50kg，女性体重≥45kg，BMI 19~28 kg/m²；
有生育能力的女性妊娠试验阴性；

排除标准:
既往有心律失常病史；
乙肝表面抗原、HIV抗体阳性；
有自杀意念或行为；
筛选前3个月内吸烟>5支/天；"""
    
    ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选
生命体征明细\tVS_SUB\t检查项\tVSTEST\t文本
生命体征明细\tVS_SUB\t结果\tVSORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选
病史明细\tMH\t病史描述\tMHTERM\t文本
病史明细\tMH\t是否存在\tMHOCCUR\t单选
心理评估\tPSYCH\t评估项目\tPSTEST\t文本
心理评估\tPSYCH\t评估结果\tPSORRES\t单选
生活习惯\tLIFESTYLE\t习惯类型\tLSTYPE\t文本
生活习惯\tLIFESTYLE\t频率数值\tLSFREQ\t数值"""
    
    try:
        result = client.run_with_realtime_logs(clinical_text, ecrf_info, max_iterations=3)
        
        print(f"\n📊 最终结果:")
        print(f"   状态: {result['status']}")
        print(f"   规则数量: {result['total_rules']}")
        print(f"   迭代次数: {result['iterations_used']}")
        
        # 显示前3条规则
        if result['rules']:
            print(f"\n📝 规则示例 (前3条):")
            for i, rule in enumerate(result['rules'][:3], 1):
                print(f"   规则 {i}: {rule.get('checkItemName', 'N/A')} - {rule.get('tableName', 'N/A')}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_batch_logs():
    """测试批量任务的日志"""
    
    print("\n🧪 测试批量任务的实时日志")
    print("=" * 60)
    
    client = RealtimeLogClient()
    
    # 提交批量任务
    batch_requests = [
        {
            "clinical_text": "入选标准: 年龄18~65周岁；体重≥50kg；",
            "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间",
            "max_iterations": 2
        },
        {
            "clinical_text": "排除标准: 妊娠或哺乳期女性；严重肝肾功能不全；",
            "ecrf_info": "实验室检查\tLB\t检查项\tLBTEST\t文本",
            "max_iterations": 2
        }
    ]
    
    try:
        response = client.session.post(f"{client.base_url}/extract/batch", json=batch_requests)
        
        if response.status_code == 200:
            batch_result = response.json()
            task_ids = batch_result["task_ids"]
            
            print(f"✅ 批量任务已提交，共 {len(task_ids)} 个任务")
            
            # 为每个任务启动日志监听
            threads = []
            for i, task_id in enumerate(task_ids, 1):
                print(f"\n🔍 监听任务 {i}: {task_id}")
                thread = threading.Thread(
                    target=client.watch_logs, 
                    args=(task_id,),
                    name=f"LogWatcher-{i}"
                )
                thread.daemon = True
                thread.start()
                threads.append(thread)
            
            # 等待所有任务完成
            for thread in threads:
                thread.join(timeout=120)  # 2分钟超时
            
            print(f"\n🎉 所有批量任务监听完成！")
        
        else:
            print(f"❌ 提交批量任务失败: {response.text}")
    
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")


if __name__ == "__main__":
    print("🎯 实时日志功能测试")
    print("=" * 80)
    
    # 测试简单案例
    test_simple_case()
    
    # 测试批量案例
    test_batch_logs()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成！")
    print("=" * 80)
