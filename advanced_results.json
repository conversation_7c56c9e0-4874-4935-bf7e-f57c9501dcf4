{"analysis_steps": "1. 识别的所有数值限制条件：\n   - 年龄范围：18~55岁\n   - 男性体重：≥50kg\n   - 女性体重：≥45kg\n   - BMI范围：19~28\n\n2. 识别的性别特异性规则：\n   - 男性体重要求\n   - 女性体重要求\n\n3. 识别的定性检查项目：\n   - 妊娠试验阴性\n\n4. 复合条件的拆分方案：\n   - 年龄范围拆分为两个条件：≥18岁和≤55岁\n   - BMI范围拆分为两个条件：≥19和≤28\n   - 体重按性别拆分为两个独立规则\n\n5. 表映射策略：\n   - 年龄通过出生日期计算，映射到DM表\n   - 性别映射到DM表\n   - 体重和BMI映射到VS_SUB表\n   - 妊娠试验映射到LBQUAL_SUB表", "rules": [{"id": 1, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": ">=", "numericValue": 18, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄<18岁，不满足入选标准"}, {"id": 2, "tableName": "DM", "checkItemOID": "BRTHDAT", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": "<=", "numericValue": 55, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄>55岁，不满足入选标准"}, {"id": 3, "tableName": "生命体征明细", "checkItemOID": "VSTEST", "checkFieldOID": "VSORRES", "checkItemName": "体重", "operator": ">=", "numericValue": 50, "nonNumericValue": null, "gender": "男", "unit": "kg", "message": "男性体重<50kg，不满足入选标准"}, {"id": 4, "tableName": "生命体征明细", "checkItemOID": "VS_SUB.VSTEST", "checkFieldOID": "VS_SUB.VSORRES", "checkItemName": "体重", "operator": ">=", "numericValue": 45, "nonNumericValue": null, "gender": "女", "unit": "kg", "message": "女性体重<45kg，不满足入选标准"}, {"id": 5, "tableName": "生命体征明细", "checkItemOID": "VS_SUB.VSTEST", "checkFieldOID": "VS_SUB.VSORRES", "checkItemName": "BMI", "operator": ">=", "numericValue": 19, "nonNumericValue": null, "gender": null, "unit": null, "message": "BMI<19，不满足入选标准"}, {"id": 6, "tableName": "生命体征明细", "checkItemOID": "VS_SUB", "checkFieldOID": "VSORRES", "checkItemName": "BMI", "operator": "<=", "numericValue": 28, "nonNumericValue": null, "gender": null, "unit": null, "message": "BMI>28，不满足入选标准"}, {"id": 7, "tableName": "实验室定性检查明细", "checkItemOID": "LBQUAL_SUB.LBTEST", "checkFieldOID": "LBQUAL_SUB.LBORRES", "checkItemName": "妊娠试验", "operator": "==", "numericValue": null, "nonNumericValue": "阴性", "gender": "女", "unit": null, "message": "妊娠试验阳性，不满足入选标准"}], "total_rules": 7}