"""
日志管理服务
"""

import queue
import time
import threading
from datetime import datetime
from typing import Generator, Dict
from models.database import db


class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.active_streams = {}  # task_id -> queue.Queue
        self.streams_lock = threading.Lock()
    
    def create_log_stream(self, task_id: str):
        """为任务创建日志流"""
        with self.streams_lock:
            self.active_streams[task_id] = queue.Queue()
    
    def add_log(self, task_id: str, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 保存到数据库
        db.add_log(task_id, message)
        
        # 添加到实时流
        with self.streams_lock:
            if task_id in self.active_streams:
                self.active_streams[task_id].put(log_entry)
    
    def get_log_stream(self, task_id: str) -> Generator[str, None, None]:
        """获取任务的实时日志流"""
        
        # 首先发送历史日志
        historical_logs = db.get_logs(task_id)
        for log in historical_logs:
            timestamp = datetime.fromisoformat(log["timestamp"]).strftime("%H:%M:%S")
            yield f"data: [{timestamp}] {log['message']}\n\n"
        
        # 如果任务已完成，直接结束
        task = db.get_task(task_id)
        if not task or task["status"] in ["completed", "failed"]:
            yield f"data: [TASK_COMPLETED] Status: {task['status'] if task else 'not_found'}\n\n"
            return
        
        # 创建实时流（如果不存在）
        with self.streams_lock:
            if task_id not in self.active_streams:
                self.active_streams[task_id] = queue.Queue()
        
        log_queue = self.active_streams[task_id]
        
        # 实时发送新日志
        while True:
            try:
                # 非阻塞获取日志
                log_entry = log_queue.get_nowait()
                yield f"data: {log_entry}\n\n"
            except queue.Empty:
                # 检查任务是否完成
                task = db.get_task(task_id)
                if task and task["status"] in ["completed", "failed"]:
                    # 任务完成，发送结束信号
                    yield f"data: [TASK_COMPLETED] Status: {task['status']}\n\n"
                    break
                
                # 短暂等待
                time.sleep(0.5)
        
        # 清理流
        self.cleanup_stream(task_id)
    
    def cleanup_stream(self, task_id: str):
        """清理任务的日志流"""
        with self.streams_lock:
            if task_id in self.active_streams:
                del self.active_streams[task_id]
    
    def cleanup_all_streams(self):
        """清理所有日志流"""
        with self.streams_lock:
            self.active_streams.clear()


# 全局日志管理器
log_manager = LogManager()
