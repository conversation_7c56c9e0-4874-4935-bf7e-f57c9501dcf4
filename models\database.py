"""
数据库模型和操作
"""

import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
import threading

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "clinical_extraction.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建任务表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    clinical_text TEXT NOT NULL,
                    ecrf_info TEXT NOT NULL,
                    max_iterations INTEGER NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT,
                    error_message TEXT,
                    iterations_used INTEGER DEFAULT 0,
                    total_rules INTEGER DEFAULT 0
                )
            """)
            
            # 创建结果表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    clinical_analysis TEXT,
                    ecrf_analysis TEXT,
                    reasoning_process TEXT,
                    validation_history TEXT,
                    optimization_history TEXT,
                    rules TEXT,
                    FOREIGN KEY (task_id) REFERENCES tasks (task_id)
                )
            """)
            
            # 创建日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    message TEXT NOT NULL,
                    FOREIGN KEY (task_id) REFERENCES tasks (task_id)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks (created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_task_id ON logs (task_id)")
            
            conn.commit()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（线程安全）"""
        with self.lock:
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            try:
                yield conn
            finally:
                conn.close()
    
    def create_task(self, task_id: str, clinical_text: str, ecrf_info: str, max_iterations: int) -> bool:
        """创建新任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO tasks (task_id, status, clinical_text, ecrf_info, max_iterations, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (task_id, "pending", clinical_text, ecrf_info, max_iterations, datetime.now().isoformat()))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建任务失败: {e}")
            return False
    
    def update_task_status(self, task_id: str, status: str, **kwargs) -> bool:
        """更新任务状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                update_fields = ["status = ?"]
                values = [status]
                
                if status == "processing" and "started_at" not in kwargs:
                    kwargs["started_at"] = datetime.now().isoformat()
                
                if status in ["completed", "failed"] and "completed_at" not in kwargs:
                    kwargs["completed_at"] = datetime.now().isoformat()
                
                for key, value in kwargs.items():
                    if key in ["started_at", "completed_at", "error_message", "iterations_used", "total_rules"]:
                        update_fields.append(f"{key} = ?")
                        values.append(value)
                
                values.append(task_id)
                
                cursor.execute(f"""
                    UPDATE tasks SET {', '.join(update_fields)}
                    WHERE task_id = ?
                """, values)
                
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新任务状态失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks WHERE task_id = ?", (task_id,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
        except Exception as e:
            print(f"获取任务失败: {e}")
            return None
    
    def get_all_tasks(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取所有任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT task_id, status, created_at, completed_at, total_rules, iterations_used
                    FROM tasks 
                    ORDER BY created_at DESC 
                    LIMIT ?
                """, (limit,))
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取任务列表失败: {e}")
            return []
    
    def save_result(self, task_id: str, result_data: Dict[str, Any]) -> bool:
        """保存抽取结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除旧结果（如果存在）
                cursor.execute("DELETE FROM results WHERE task_id = ?", (task_id,))
                
                # 插入新结果
                cursor.execute("""
                    INSERT INTO results (
                        task_id, clinical_analysis, ecrf_analysis, reasoning_process,
                        validation_history, optimization_history, rules
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id,
                    result_data.get("clinical_analysis"),
                    result_data.get("ecrf_analysis"),
                    result_data.get("reasoning_process"),
                    json.dumps(result_data.get("validation_history", []), ensure_ascii=False),
                    json.dumps(result_data.get("optimization_history", []), ensure_ascii=False),
                    json.dumps(result_data.get("rules", []), ensure_ascii=False)
                ))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"保存结果失败: {e}")
            return False
    
    def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取抽取结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM results WHERE task_id = ?", (task_id,))
                row = cursor.fetchone()
                
                if row:
                    result = dict(row)
                    # 解析JSON字段
                    if result["validation_history"]:
                        result["validation_history"] = json.loads(result["validation_history"])
                    if result["optimization_history"]:
                        result["optimization_history"] = json.loads(result["optimization_history"])
                    if result["rules"]:
                        result["rules"] = json.loads(result["rules"])
                    
                    return result
                return None
        except Exception as e:
            print(f"获取结果失败: {e}")
            return None
    
    def add_log(self, task_id: str, message: str) -> bool:
        """添加日志"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO logs (task_id, timestamp, message)
                    VALUES (?, ?, ?)
                """, (task_id, datetime.now().isoformat(), message))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加日志失败: {e}")
            return False
    
    def get_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT timestamp, message 
                    FROM logs 
                    WHERE task_id = ? 
                    ORDER BY timestamp ASC
                """, (task_id,))
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取日志失败: {e}")
            return []
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务及相关数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除相关数据
                cursor.execute("DELETE FROM logs WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM results WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM tasks WHERE task_id = ?", (task_id,))
                
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除任务失败: {e}")
            return False
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """清理旧任务"""
        try:
            from datetime import timedelta
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取要删除的任务ID
                cursor.execute("""
                    SELECT task_id FROM tasks 
                    WHERE created_at < ? AND status IN ('completed', 'failed')
                """, (cutoff_date,))
                
                task_ids = [row[0] for row in cursor.fetchall()]
                
                # 删除相关数据
                for task_id in task_ids:
                    cursor.execute("DELETE FROM logs WHERE task_id = ?", (task_id,))
                    cursor.execute("DELETE FROM results WHERE task_id = ?", (task_id,))
                    cursor.execute("DELETE FROM tasks WHERE task_id = ?", (task_id,))
                
                conn.commit()
                return len(task_ids)
        except Exception as e:
            print(f"清理旧任务失败: {e}")
            return 0


# 全局数据库实例
db = DatabaseManager()
