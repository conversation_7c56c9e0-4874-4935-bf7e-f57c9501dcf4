{"analysis_steps": "1. 识别的所有数值限制条件:\n   - 年龄: 18~55岁\n   - 男性体重: ≥50kg\n   - 女性体重: ≥45kg\n   - BMI: 19~28\n\n2. 识别的性别特异性规则:\n   - 男性体重要求\n   - 女性体重要求\n\n3. 识别的定性检查项目:\n   - 妊娠试验: 阴性\n   - 乙肝表面抗原: 阴性\n\n4. 复合条件的拆分方案:\n   - 年龄范围拆分为两个规则: ≥18且≤55\n   - BMI范围拆分为两个规则: ≥19且≤28\n   - 性别特异性体重要求分别创建\n\n5. 表映射策略:\n   - 年龄: DM表通过出生日期计算\n   - 体重/BMI: LB_SUB表\n   - 妊娠试验: LB_SUB表\n   - 乙肝表面抗原: LBVT_SUB表", "rules": [{"id": 1, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": ">=", "numericValue": 18, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄<18岁，不满足入选标准"}, {"id": 2, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": "<=", "numericValue": 55, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄>55岁，不满足入选标准"}, {"id": 3, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "体重", "operator": ">=", "numericValue": 50, "nonNumericValue": null, "gender": "男", "unit": "kg", "message": "男性体重<50kg，不满足入选标准"}, {"id": 4, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "DM.WEIGHT", "checkItemName": "体重", "operator": ">=", "numericValue": 45, "nonNumericValue": null, "gender": "女", "unit": "kg", "message": "女性体重<45kg，不满足入选标准"}, {"id": 5, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "BMI", "operator": ">=", "numericValue": 19, "nonNumericValue": null, "gender": null, "unit": null, "message": "BMI<19，不满足入选标准"}, {"id": 6, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "BMI", "operator": "<=", "numericValue": 28, "nonNumericValue": null, "gender": null, "unit": null, "message": "BMI>28，不满足入选标准"}, {"id": 7, "tableName": "实验室检查明细", "checkItemOID": "LB_SUB", "checkFieldOID": "LBTEST", "checkItemName": "妊娠试验", "operator": "==", "numericValue": null, "nonNumericValue": "阴性", "gender": "女", "unit": null, "message": "妊娠试验阳性，不满足入选标准"}, {"id": 8, "tableName": "病毒学定量检查明细", "checkItemOID": "LBVT_SUB.LBTEST", "checkFieldOID": "LBVT_SUB.LBORRES", "checkItemName": "乙肝表面抗原", "operator": "==", "numericValue": null, "nonNumericValue": "阴性", "gender": null, "unit": null, "message": "乙肝表面抗原阳性，不满足入选标准"}], "total_rules": 8}