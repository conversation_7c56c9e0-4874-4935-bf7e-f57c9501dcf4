"""
API模型定义
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime


class ExtractionRequest(BaseModel):
    """抽取请求模型"""
    clinical_text: str = Field(..., description="临床试验的入组和排除标准文本")
    ecrf_info: str = Field(..., description="eCRF表结构信息")
    max_iterations: Optional[int] = Field(3, description="最大迭代次数", ge=1, le=5)


class ExtractionRule(BaseModel):
    """抽取规则模型"""
    id: int
    tableName: str
    checkItemOID: str
    checkFieldOID: str
    checkItemName: str
    operator: str
    numericValue: Optional[Any] = None
    nonNumericValue: Optional[str] = None
    gender: Optional[str] = None
    unit: Optional[str] = None
    message: str
    clinicalRationale: str


class ExtractionResponse(BaseModel):
    """抽取响应模型"""
    task_id: str
    status: str
    clinical_analysis: Optional[str] = None
    ecrf_analysis: Optional[str] = None
    reasoning_process: Optional[str] = None
    validation_history: Optional[List[str]] = None
    optimization_history: Optional[List[str]] = None
    iterations_used: Optional[int] = None
    rules: List[ExtractionRule] = []
    total_rules: int = 0
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error: Optional[str] = None


class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str
    progress: str
    created_at: str
    completed_at: Optional[str] = None
    total_rules: Optional[int] = None
    iterations_used: Optional[int] = None


class LogEntry(BaseModel):
    """日志条目模型"""
    timestamp: str
    message: str


class TaskLogs(BaseModel):
    """任务日志模型"""
    task_id: str
    logs: List[LogEntry]


class BatchTaskResponse(BaseModel):
    """批量任务响应模型"""
    task_ids: List[str]
    total_tasks: int
    status: str
    message: str


def create_extraction_response(task_data: Dict[str, Any], result_data: Optional[Dict[str, Any]] = None) -> ExtractionResponse:
    """从数据库数据创建抽取响应"""
    
    response = ExtractionResponse(
        task_id=task_data["task_id"],
        status=task_data["status"],
        created_at=task_data["created_at"],
        started_at=task_data.get("started_at"),
        completed_at=task_data.get("completed_at"),
        iterations_used=task_data.get("iterations_used", 0),
        total_rules=task_data.get("total_rules", 0),
        error=task_data.get("error_message"),
        rules=[],
        validation_history=[],
        optimization_history=[]
    )
    
    # 添加结果数据
    if result_data:
        response.clinical_analysis = result_data.get("clinical_analysis")
        response.ecrf_analysis = result_data.get("ecrf_analysis")
        response.reasoning_process = result_data.get("reasoning_process")
        response.validation_history = result_data.get("validation_history", [])
        response.optimization_history = result_data.get("optimization_history", [])
        
        # 转换规则
        rules = result_data.get("rules", [])
        if rules:
            response.rules = [ExtractionRule(**rule) for rule in rules]
            response.total_rules = len(response.rules)
    
    return response
