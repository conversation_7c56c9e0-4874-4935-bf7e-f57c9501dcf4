"""
优化后的LLM驱动临床信息抽取器
根据用户反馈进行优化：
1. 移除max_tokens限制
2. 简化验证指标（只保留完整性、准确性、逻辑性）
3. 移除冗余的最终验证器
"""

import dspy
import json
from typing import List, Dict, Any


# 配置LLM模型 - 移除max_tokens限制
lm = dspy.LM("openai/DeepSeek-V3",
             api_key="sP8TpUbNGBZfEYI",
             api_base="https://llmaas.hengrui.com/v1",
             cache=False,
             max_tokens=65535)  # 增加token限制
dspy.configure(lm=lm)


class ClinicalTextAnalyzer(dspy.Signature):
    """临床文本智能分析器"""
    
    clinical_text: str = dspy.InputField(desc="临床试验的入组和排除标准文本")
    
    comprehensive_analysis: str = dspy.OutputField(
        desc="""对临床文本进行全面深度分析，包括：
        
        1. 文本结构分析：识别入组标准、排除标准的组织结构
        2. 医学概念识别：提取所有医学术语、检查项目、疾病名称
        3. 数值条件识别：找出所有数值限制、范围、阈值
        4. 逻辑关系分析：理解条件之间的逻辑关系（AND、OR、NOT）
        5. 隐含条件推理：推断文本中隐含但未明确表达的检查要求
        6. 临床意义解释：解释每个条件的临床意义和检查目的
        
        请用自然语言详细描述你的分析过程和发现。"""
    )


class ECRFStructureAnalyzer(dspy.Signature):
    """eCRF结构智能分析器"""
    
    ecrf_info: str = dspy.InputField(desc="eCRF表结构信息")
    
    structure_analysis: str = dspy.OutputField(
        desc="""深度分析eCRF结构，包括：
        
        1. 表结构解析：理解每个表的用途和数据类型
        2. 字段语义分析：理解每个字段的医学含义
        3. 数据关系推理：推断表之间的关系和数据流
        4. 适用场景识别：判断每个表适合存储哪类检查数据
        5. 字段映射策略：为不同类型的检查项目推荐最佳字段
        
        请详细分析eCRF的结构和用途。"""
    )


class IntelligentRuleGenerator(dspy.Signature):
    """智能规则生成器"""
    
    clinical_analysis: str = dspy.InputField(desc="临床文本的深度分析结果")
    ecrf_analysis: str = dspy.InputField(desc="eCRF结构的深度分析结果")
    
    reasoning_process: str = dspy.OutputField(
        desc="""详细的推理过程，包括：
        
        1. 条件识别：从临床分析中识别出的所有检查条件
        2. 表映射推理：为每个条件选择最合适的eCRF表的推理过程
        3. 字段选择逻辑：选择具体字段的逻辑和原因
        4. 操作符确定：确定比较操作符的推理过程
        5. 数值提取：提取和标准化数值的过程
        6. 特殊情况处理：处理复杂条件、性别特异性、范围条件等
        
        请详细说明你的推理过程。"""
    )
    
    extracted_rules: str = dspy.OutputField(
        desc="""基于深度分析和推理，生成完整的检查规则JSON数组。
        
        每个规则必须包含：
        - id: 规则编号
        - tableName: 表名（基于eCRF分析选择）
        - checkItemOID: 检查项目OID
        - checkFieldOID: 检查字段OID  
        - checkItemName: 检查项目名称
        - operator: 操作符（<, <=, >, >=, =, !=, BETWEEN等）
        - numericValue: 数值（单个数值或数组）
        - nonNumericValue: 非数值（如"阴性"、"正常"等）
        - gender: 性别限制（Male/Female/null）
        - unit: 单位
        - message: 检查消息
        - clinicalRationale: 临床依据和意义
        
        请确保：
        1. 涵盖所有可检查的条件
        2. 正确处理复合条件和范围
        3. 准确映射到eCRF结构
        4. 提供清晰的临床依据
        
        输出格式必须是有效的JSON数组。"""
    )


class RuleValidator(dspy.Signature):
    """规则验证器 - 简化版本"""
    
    original_text: str = dspy.InputField(desc="原始临床文本")
    generated_rules: str = dspy.InputField(desc="生成的规则JSON")
    
    validation_report: str = dspy.OutputField(
        desc="""简化的验证报告，包括：
        
        1. 完整性检查：是否遗漏了重要的检查条件
        2. 准确性验证：规则是否准确反映了原文意图
        3. 逻辑一致性：规则之间是否存在逻辑冲突
        
        请提供详细的验证分析和改进建议。
        
        最后必须明确输出验证结果：
        - 如果完全合格，输出"验证通过"
        - 如果仍有问题，输出"验证不通过"并详细说明问题"""
    )
    
    optimized_rules: str = dspy.OutputField(
        desc="""基于验证分析，输出优化后的规则JSON数组。
        
        确保：
        1. 补充遗漏的条件
        2. 修正不准确的规则
        3. 解决逻辑冲突
        
        输出格式必须是有效的JSON数组。"""
    )


class IterativeRuleOptimizer(dspy.Signature):
    """迭代规则优化器"""
    
    original_text: str = dspy.InputField(desc="原始临床文本")
    current_rules: str = dspy.InputField(desc="当前规则JSON")
    validation_issues: str = dspy.InputField(desc="验证发现的问题")
    ecrf_structure: str = dspy.InputField(desc="eCRF结构信息")
    
    optimization_analysis: str = dspy.OutputField(
        desc="""详细的优化分析，包括：
        
        1. 问题分析：逐一分析验证报告中的每个问题
        2. 解决方案：为每个问题提供具体的解决方案
        3. 新增规则：需要添加的遗漏规则
        4. 修正规则：需要修正的现有规则
        5. 优化策略：整体的优化策略和方法
        
        请提供详细的优化分析。"""
    )
    
    optimized_rules: str = dspy.OutputField(
        desc="""基于问题分析，输出完整优化后的规则JSON数组。
        
        确保：
        1. 补充所有遗漏的条件
        2. 修正所有不准确的规则
        3. 解决所有逻辑冲突
        4. 覆盖原文中的所有检查要求
        5. 每个规则都有清晰的临床依据
        
        输出格式必须是有效的JSON数组。"""
    )


class OptimizedLLMExtractor:
    """优化后的LLM驱动抽取器"""
    
    def __init__(self, max_iterations: int = 3):
        self.text_analyzer = dspy.ChainOfThought(ClinicalTextAnalyzer)
        self.ecrf_analyzer = dspy.ChainOfThought(ECRFStructureAnalyzer)
        self.rule_generator = dspy.ChainOfThought(IntelligentRuleGenerator)
        self.rule_validator = dspy.ChainOfThought(RuleValidator)
        self.iterative_optimizer = dspy.ChainOfThought(IterativeRuleOptimizer)
        self.max_iterations = max_iterations
    
    def extract_with_full_analysis(self, clinical_text: str, ecrf_info: str) -> Dict[str, Any]:
        """完整的智能分析和抽取流程，包含迭代优化"""
        
        print("🧠 第1步：深度分析临床文本...")
        clinical_analysis = self.text_analyzer(clinical_text=clinical_text)
        
        print("🏗️ 第2步：深度分析eCRF结构...")
        ecrf_analysis = self.ecrf_analyzer(ecrf_info=ecrf_info)
        
        print("⚡ 第3步：智能生成检查规则...")
        rule_generation = self.rule_generator(
            clinical_analysis=clinical_analysis.comprehensive_analysis,
            ecrf_analysis=ecrf_analysis.structure_analysis
        )
        
        print("✅ 第4步：迭代验证和优化...")
        current_rules = rule_generation.extracted_rules
        validation_history = []
        optimization_history = []
        
        for iteration in range(self.max_iterations):
            print(f"   🔄 迭代 {iteration + 1}/{self.max_iterations}...")
            
            # 验证当前规则
            validation = self.rule_validator(
                original_text=clinical_text,
                generated_rules=current_rules
            )
            validation_history.append(validation.validation_report)
            
            # 检查是否通过验证
            if "验证通过" in validation.validation_report:
                print(f"   ✅ 第{iteration + 1}次迭代通过验证！")
                current_rules = validation.optimized_rules
                break
            else:
                print(f"   ❌ 第{iteration + 1}次迭代未通过验证")
                
                # 如果未通过且不是最后一次迭代，进行优化
                if iteration < self.max_iterations - 1:
                    print(f"   🔧 发现问题，进行优化...")
                    optimization = self.iterative_optimizer(
                        original_text=clinical_text,
                        current_rules=current_rules,
                        validation_issues=validation.validation_report,
                        ecrf_structure=ecrf_info
                    )
                    optimization_history.append(optimization.optimization_analysis)
                    current_rules = optimization.optimized_rules
                else:
                    print(f"   ⚠️ 达到最大迭代次数，使用当前最佳结果")
                    current_rules = validation.optimized_rules
        
        # 解析最终规则
        try:
            final_rules = json.loads(current_rules)
            if not isinstance(final_rules, list):
                final_rules = [final_rules] if final_rules else []
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
            final_rules = []
        
        return {
            "clinical_analysis": clinical_analysis.comprehensive_analysis,
            "ecrf_analysis": ecrf_analysis.structure_analysis,
            "reasoning_process": rule_generation.reasoning_process,
            "validation_history": validation_history,
            "optimization_history": optimization_history,
            "iterations_used": iteration + 1 if 'iteration' in locals() else 0,
            "rules": final_rules,
            "total_rules": len(final_rules)
        }
    
    def extract_rules_only(self, clinical_text: str, ecrf_info: str) -> List[Dict[str, Any]]:
        """简化接口：只返回规则"""
        result = self.extract_with_full_analysis(clinical_text, ecrf_info)
        return result.get("rules", [])


def test_optimized_extractor():
    """测试优化后的抽取器"""
    
    print("🎯 测试优化后的LLM抽取器")
    print("=" * 60)
    
    # 创建抽取器
    extractor = OptimizedLLMExtractor(max_iterations=3)
    
    # 测试案例
    test_text = """入选标准:
1. 年龄18~55周岁，男女均可；
2. 男性体重≥50kg，女性体重≥45kg，BMI 19~28 kg/m²；
3. 有生育能力的女性妊娠试验阴性；

排除标准:
1. 既往有心律失常病史；
2. 乙肝表面抗原、HIV抗体阳性；
3. 有自杀意念或行为；
4. 筛选前3个月内吸烟>5支/天；"""
    
    ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选
生命体征明细\tVS_SUB\t检查项\tVSTEST\t文本
生命体征明细\tVS_SUB\t结果\tVSORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选
病史明细\tMH\t病史描述\tMHTERM\t文本
病史明细\tMH\t是否存在\tMHOCCUR\t单选
心理评估\tPSYCH\t评估项目\tPSTEST\t文本
心理评估\tPSYCH\t评估结果\tPSORRES\t单选
生活习惯\tLIFESTYLE\t习惯类型\tLSTYPE\t文本
生活习惯\tLIFESTYLE\t频率数值\tLSFREQ\t数值"""
    
    # 执行完整分析
    result = extractor.extract_with_full_analysis(test_text, ecrf_info)
    
    print(f"\n🎯 最终结果: 成功提取了 {result['total_rules']} 条规则")
    print(f"   迭代次数: {result['iterations_used']}")
    
    # 显示部分规则
    for i, rule in enumerate(result["rules"][:5], 1):
        print(f"\n规则 {i}:")
        print(f"  检查项: {rule.get('checkItemName', 'N/A')}")
        print(f"  表名: {rule.get('tableName', 'N/A')}")
        print(f"  操作符: {rule.get('operator', 'N/A')}")
        print(f"  数值: {rule.get('numericValue', rule.get('nonNumericValue', 'N/A'))}")
        print(f"  临床依据: {rule.get('clinicalRationale', 'N/A')}")
    
    # 保存结果
    with open("optimized_results.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: optimized_results.json")
    
    return result


if __name__ == "__main__":
    test_optimized_extractor()
