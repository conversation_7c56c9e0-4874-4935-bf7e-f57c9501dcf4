import dspy




lm = dspy.LM("openai/DeepSeek-V3", 
             api_key="sP8TpUbNGBZfEYI", 
             api_base="https://llmaas.hengrui.com/v1",
             cache=False
             )
dspy.configure(lm=lm)


# print(lm("Say this is a test!", temperature=0.7))
# print(lm.history)
# lm(messages=[{"role": "user", "content": "Say this is a test!"}])  # => ['This is a test!']


# 数学

# math = dspy.ChainOfThought("question -> answer: float")
# print(math.predict(question="Two dice are tossed. What is the probability that the sum equals two?"))

# 信息抽取
# class ExtractInfo(dspy.Signature):
#     """Extract structured information from text."""

#     text: str = dspy.InputField()
#     title: str = dspy.OutputField()
#     headings: list[str] = dspy.OutputField()
#     entities: list[dict[str, str]] = dspy.OutputField(desc="a list of entities and their metadata")

# module = dspy.Predict(ExtractInfo)

# text = "Apple Inc. announced its latest iPhone 14 today." \
#     "The CEO, <PERSON>, highlighted its new features in a press release."
# response = module(text=text)

# print(response.title)
# print(response.headings)
# print(response.entities)


# agent

# def evaluate_math(expression: str):
#     return dspy.PythonInterpreter({}).execute(expression)

# def search_wikipedia(query: str):
#     results = dspy.ColBERTv2(url="https://github.com/eigent-ai/eigent/tree/main?tab=readme-ov-file")(query, k=3)
#     return [x["text"] for x in results]

# react = dspy.ReAct("question -> answer: float", tools=[evaluate_math, search_wikipedia])

# pred = react(question="Context Engineering 有几个issues?")
# print(pred.answer)