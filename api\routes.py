"""
API路由定义
"""

import uuid
import asyncio
from datetime import datetime
from typing import List
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse

from models.schemas import (
    ExtractionRequest, ExtractionResponse, TaskStatus, 
    BatchTaskResponse, create_extraction_response
)
from models.database import db
from services.log_manager import log_manager
from services.extraction_service import extraction_service

router = APIRouter()


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@router.post("/extract", response_model=dict)
async def extract_clinical_info(request: ExtractionRequest):
    """
    提交临床信息抽取任务（完全异步）
    
    - **clinical_text**: 临床试验的入组和排除标准文本
    - **ecrf_info**: eCRF表结构信息
    - **max_iterations**: 最大迭代次数（1-5）
    """
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 创建任务记录
    success = db.create_task(
        task_id=task_id,
        clinical_text=request.clinical_text,
        ecrf_info=request.ecrf_info,
        max_iterations=request.max_iterations
    )
    
    if not success:
        raise HTTPException(status_code=500, detail="创建任务失败")
    
    # 创建日志流
    log_manager.create_log_stream(task_id)
    log_manager.add_log(task_id, "🎯 任务已创建，开始处理...")
    
    # 异步启动抽取任务
    asyncio.create_task(extraction_service.process_extraction_async(
        task_id,
        request.clinical_text,
        request.ecrf_info,
        request.max_iterations
    ))
    
    return {
        "task_id": task_id,
        "status": "pending",
        "message": "抽取任务已提交，请使用task_id查询结果"
    }


@router.get("/extract/{task_id}", response_model=ExtractionResponse)
async def get_extraction_result(task_id: str):
    """
    获取抽取任务结果
    
    - **task_id**: 任务ID
    """
    
    # 获取任务信息
    task_data = db.get_task(task_id)
    if not task_data:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 获取结果数据
    result_data = None
    if task_data["status"] == "completed":
        result_data = db.get_result(task_id)
    
    return create_extraction_response(task_data, result_data)


@router.get("/extract/{task_id}/logs")
async def get_extraction_logs(task_id: str):
    """
    获取抽取任务的实时日志流 (Server-Sent Events)
    
    - **task_id**: 任务ID
    """
    
    # 检查任务是否存在
    task_data = db.get_task(task_id)
    if not task_data:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return StreamingResponse(
        log_manager.get_log_stream(task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/tasks", response_model=List[TaskStatus])
async def list_tasks(limit: int = 100):
    """获取所有任务状态"""
    
    tasks = db.get_all_tasks(limit)
    
    return [
        TaskStatus(
            task_id=task["task_id"],
            status=task["status"],
            progress=f"已提取{task['total_rules']}条规则" if task["status"] == "completed" else task["status"],
            created_at=task["created_at"],
            completed_at=task.get("completed_at"),
            total_rules=task.get("total_rules"),
            iterations_used=task.get("iterations_used")
        )
        for task in tasks
    ]


@router.delete("/tasks/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    
    success = db.delete_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 清理日志流
    log_manager.cleanup_stream(task_id)
    
    return {"message": "任务已删除"}


@router.post("/extract/batch", response_model=BatchTaskResponse)
async def extract_batch(requests: List[ExtractionRequest]):
    """
    批量提交抽取任务（高并发）
    
    - **requests**: 抽取请求列表
    """
    
    if len(requests) > 50:  # 限制批量大小
        raise HTTPException(status_code=400, detail="批量任务数量不能超过50个")
    
    task_ids = []
    
    for i, request in enumerate(requests, 1):
        # 生成任务ID
        task_id = str(uuid.uuid4())
        task_ids.append(task_id)
        
        # 创建任务记录
        success = db.create_task(
            task_id=task_id,
            clinical_text=request.clinical_text,
            ecrf_info=request.ecrf_info,
            max_iterations=request.max_iterations
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"创建第{i}个任务失败")
        
        # 创建日志流
        log_manager.create_log_stream(task_id)
        log_manager.add_log(task_id, f"🎯 批量任务 {i}/{len(requests)} 已创建")
        
        # 异步启动抽取任务
        asyncio.create_task(extraction_service.process_extraction_async(
            task_id,
            request.clinical_text,
            request.ecrf_info,
            request.max_iterations
        ))
    
    return BatchTaskResponse(
        task_ids=task_ids,
        total_tasks=len(task_ids),
        status="pending",
        message=f"已提交{len(task_ids)}个批量抽取任务"
    )


@router.post("/admin/cleanup")
async def cleanup_old_tasks(days: int = 30):
    """清理旧任务（管理员接口）"""
    
    if days < 1:
        raise HTTPException(status_code=400, detail="天数必须大于0")
    
    deleted_count = db.cleanup_old_tasks(days)
    
    return {
        "message": f"已清理 {deleted_count} 个超过 {days} 天的旧任务",
        "deleted_count": deleted_count
    }
