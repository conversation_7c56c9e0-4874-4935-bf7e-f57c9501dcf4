"""
DSPy优化器模块
用于提升临床信息抽取的性能
"""

import dspy
import json
import time
from typing import List, Dict, Any
from clinical_extractor import ClinicalExtractor
from training_manager import TrainingManager


def create_training_examples() -> List[dspy.Example]:
    """创建训练示例数据集（使用训练管理器）"""

    # 使用训练管理器获取示例
    manager = TrainingManager()
    dspy_examples = manager.get_dspy_examples()

    print(f"📚 从训练管理器加载了 {len(dspy_examples)} 个训练示例")

    # 如果没有示例，创建默认示例
    if not dspy_examples:
        print("⚠️ 未找到训练示例，使用内置默认示例")
        return _create_default_examples()
    else:
        return dspy_examples


def _create_default_examples() -> List[dspy.Example]:
    """创建默认的训练示例"""

    examples = []
    
    # 示例1：年龄和ECOG评分
    example1_text = """入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
排除标准:
年龄不在18-70周岁范围内的患者；
ECOG评分>1分的患者；"""
    
    example1_ecrf = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选"""
    
    example1_expected = [
        {
            "id": 1,
            "tableName": "DM",
            "checkItemOID": "DM.BRTHDAT",
            "checkFieldOID": "BRTHDAT",
            "checkItemName": "年龄",
            "operator": "<",
            "numericValue": 18,
            "nonNumericValue": None,
            "gender": None,
            "unit": "周岁",
            "message": "年龄<18周岁，不满足入选标准，请核实"
        },
        {
            "id": 2,
            "tableName": "DM",
            "checkItemOID": "DM.BRTHDAT",
            "checkFieldOID": "BRTHDAT",
            "checkItemName": "年龄",
            "operator": ">",
            "numericValue": 70,
            "nonNumericValue": None,
            "gender": None,
            "unit": "周岁",
            "message": "年龄>70周岁，不满足入选标准，请核实"
        },
        {
            "id": 3,
            "tableName": "RSECOG",
            "checkItemOID": "RSECOG.RSORRES",
            "checkFieldOID": "RSORRES",
            "checkItemName": "ECOG评分",
            "operator": ">",
            "numericValue": 1,
            "nonNumericValue": None,
            "gender": None,
            "unit": None,
            "message": "ECOG评分>1分，满足排除标准，请核实"
        }
    ]
    
    # 示例2：实验室检查
    example2_text = """入选标准:
骨髓功能基本正常：
ANC≥1.0×10^9/L；
血小板计数≥75×10^9/L；
排除标准:
ANC<1.0×10^9/L的患者；
血小板计数<75×10^9/L的患者；"""
    
    example2_ecrf = """实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值"""
    
    example2_expected = [
        {
            "id": 1,
            "tableName": "LB_SUB",
            "checkItemOID": "LB_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "ANC",
            "operator": "<",
            "numericValue": 1.0,
            "nonNumericValue": None,
            "gender": None,
            "unit": "10^9/L",
            "message": "ANC<1.0×10^9/L，不满足入选标准，请核实"
        },
        {
            "id": 2,
            "tableName": "LB_SUB",
            "checkItemOID": "LB_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "血小板计数",
            "operator": "<",
            "numericValue": 75,
            "nonNumericValue": None,
            "gender": None,
            "unit": "10^9/L",
            "message": "血小板计数<75×10^9/L，不满足入选标准，请核实"
        }
    ]
    
    # 示例3：心电图检查（性别特异性）
    example3_text = """排除标准:
心电图QTc间期延长（男性>450ms、女性>470ms）；"""
    
    example3_ecrf = """12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值"""
    
    example3_expected = [
        {
            "id": 1,
            "tableName": "EG_SUB",
            "checkItemOID": "EG_SUB.EGTEST",
            "checkFieldOID": "EGORRES",
            "checkItemName": "QTc间期",
            "operator": ">",
            "numericValue": 450,
            "nonNumericValue": None,
            "gender": "Male",
            "unit": "ms",
            "message": "男性QTc间期>450ms，满足排除标准，请核实"
        },
        {
            "id": 2,
            "tableName": "EG_SUB",
            "checkItemOID": "EG_SUB.EGTEST",
            "checkFieldOID": "EGORRES",
            "checkItemName": "QTc间期",
            "operator": ">",
            "numericValue": 470,
            "nonNumericValue": None,
            "gender": "Female",
            "unit": "ms",
            "message": "女性QTc间期>470ms，满足排除标准，请核实"
        }
    ]
    
    # 示例4：肝肾功能检查
    example4_text = """入选标准:
肝肾功能基本正常：
血清肌酐≤1.5×ULN；
血清总胆红素≤1.5×ULN；
AST和ALT≤3×ULN；
排除标准:
肌酐>1.5×ULN的患者；
总胆红素>1.5×ULN的患者；"""

    example4_ecrf = """实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值"""

    example4_expected = [
        {
            "id": 1,
            "tableName": "LB_SUB",
            "checkItemOID": "LB_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "血清肌酐",
            "operator": ">",
            "numericValue": 1.5,
            "nonNumericValue": None,
            "gender": None,
            "unit": "ULN",
            "message": "血清肌酐>1.5×ULN，满足排除标准，请核实"
        },
        {
            "id": 2,
            "tableName": "LB_SUB",
            "checkItemOID": "LB_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "血清总胆红素",
            "operator": ">",
            "numericValue": 1.5,
            "nonNumericValue": None,
            "gender": None,
            "unit": "ULN",
            "message": "血清总胆红素>1.5×ULN，满足排除标准，请核实"
        }
    ]

    # 示例5：病毒学检查
    example5_text = """排除标准:
已知HBV或HCV感染活动期：
HBV DNA≥2×10³ IU/mL；
HCV RNA≥10³ IU/mL；
HIV血清检测阳性；"""

    example5_ecrf = """病毒学定量检查明细\tLBVT_SUB\t检查项\tLBTEST\t文本
病毒学定量检查明细\tLBVT_SUB\t检测值\tLBORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选"""

    example5_expected = [
        {
            "id": 1,
            "tableName": "LBVT_SUB",
            "checkItemOID": "LBVT_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "HBV DNA",
            "operator": ">=",
            "numericValue": 2000,
            "nonNumericValue": None,
            "gender": None,
            "unit": "IU/mL",
            "message": "HBV DNA≥2×10³ IU/mL，满足排除标准，请核实"
        },
        {
            "id": 2,
            "tableName": "LBVT_SUB",
            "checkItemOID": "LBVT_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "HCV RNA",
            "operator": ">=",
            "numericValue": 1000,
            "nonNumericValue": None,
            "gender": None,
            "unit": "IU/mL",
            "message": "HCV RNA≥10³ IU/mL，满足排除标准，请核实"
        },
        {
            "id": 3,
            "tableName": "LBQUAL_SUB",
            "checkItemOID": "LBQUAL_SUB.LBTEST",
            "checkFieldOID": "LBORRES",
            "checkItemName": "HIV血清检测",
            "operator": "=",
            "numericValue": None,
            "nonNumericValue": "阳性",
            "gender": None,
            "unit": None,
            "message": "HIV血清检测阳性，满足排除标准，请核实"
        }
    ]

    # 示例6：简单年龄检查
    example6_text = """入选标准:
年龄≥18岁。
排除标准:
年龄<18岁的患者。"""

    example6_ecrf = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间"""

    example6_expected = [
        {
            "id": 1,
            "tableName": "DM",
            "checkItemOID": "DM.BRTHDAT",
            "checkFieldOID": "BRTHDAT",
            "checkItemName": "年龄",
            "operator": "<",
            "numericValue": 18,
            "nonNumericValue": None,
            "gender": None,
            "unit": "岁",
            "message": "年龄<18岁，不满足入选标准，请核实"
        }
    ]

    # 创建DSPy Example对象
    examples = [
        dspy.Example(
            clinical_text=example1_text,
            ecrf_info=example1_ecrf,
            expected_rules=example1_expected
        ).with_inputs("clinical_text", "ecrf_info"),

        dspy.Example(
            clinical_text=example2_text,
            ecrf_info=example2_ecrf,
            expected_rules=example2_expected
        ).with_inputs("clinical_text", "ecrf_info"),

        dspy.Example(
            clinical_text=example3_text,
            ecrf_info=example3_ecrf,
            expected_rules=example3_expected
        ).with_inputs("clinical_text", "ecrf_info"),

        dspy.Example(
            clinical_text=example4_text,
            ecrf_info=example4_ecrf,
            expected_rules=example4_expected
        ).with_inputs("clinical_text", "ecrf_info"),

        dspy.Example(
            clinical_text=example5_text,
            ecrf_info=example5_ecrf,
            expected_rules=example5_expected
        ).with_inputs("clinical_text", "ecrf_info"),

        dspy.Example(
            clinical_text=example6_text,
            ecrf_info=example6_ecrf,
            expected_rules=example6_expected
        ).with_inputs("clinical_text", "ecrf_info")
    ]

    return examples


def extraction_metric(example, prediction, trace=None):
    """评估抽取质量的指标函数"""
    
    try:
        expected_rules = example.expected_rules
        predicted_rules = prediction
        
        if not isinstance(predicted_rules, list):
            return 0.0
        
        if not expected_rules:
            return 1.0 if not predicted_rules else 0.5
        
        # 计算匹配分数
        total_score = 0.0
        
        for expected_rule in expected_rules:
            best_match_score = 0.0
            
            for predicted_rule in predicted_rules:
                if isinstance(predicted_rule, dict):
                    match_score = calculate_rule_similarity(expected_rule, predicted_rule)
                    best_match_score = max(best_match_score, match_score)
            
            total_score += best_match_score
        
        # 计算平均得分
        base_score = total_score / len(expected_rules)
        
        # 温和的惩罚机制
        if len(predicted_rules) > len(expected_rules) * 1.5:
            penalty = min(0.1, (len(predicted_rules) - len(expected_rules)) * 0.01)
            base_score = max(0.0, base_score - penalty)
        
        # 奖励机制
        if base_score >= 0.8:
            base_score = min(1.0, base_score + 0.05)
        
        return base_score
        
    except Exception as e:
        print(f"评估指标计算错误: {e}")
        return 0.0


def calculate_rule_similarity(expected: dict, predicted: dict) -> float:
    """计算规则相似度"""
    
    score = 0.0
    
    # 检查项目名称匹配 (权重: 0.4)
    exp_name = expected.get("checkItemName", "").lower()
    pred_name = predicted.get("checkItemName", "").lower()
    
    if exp_name == pred_name:
        score += 0.4
    elif exp_name and pred_name:
        # 关键词匹配
        exp_keywords = set(exp_name.split())
        pred_keywords = set(pred_name.split())
        overlap = len(exp_keywords & pred_keywords)
        if overlap > 0:
            score += 0.4 * (overlap / max(len(exp_keywords), len(pred_keywords)))
    
    # 操作符匹配 (权重: 0.3)
    exp_op = expected.get("operator", "")
    pred_op = predicted.get("operator", "")
    
    if exp_op == pred_op:
        score += 0.3
    elif exp_op and pred_op:
        # 操作符语义匹配
        op_groups = [
            ["<", "≤", "<="],
            [">", "≥", ">="],
            ["=", "=="],
            ["!=", "≠"]
        ]
        for group in op_groups:
            if exp_op in group and pred_op in group:
                score += 0.25
                break
    
    # 数值匹配 (权重: 0.2)
    exp_val = expected.get("numericValue")
    pred_val = predicted.get("numericValue")
    
    if exp_val is not None and pred_val is not None:
        if abs(exp_val - pred_val) < 0.01:
            score += 0.2
        elif abs(exp_val - pred_val) / max(abs(exp_val), 1) < 0.2:
            score += 0.15
    elif exp_val is None and pred_val is None:
        score += 0.2
    
    # 其他字段
    if expected.get("unit") == predicted.get("unit"):
        score += 0.05
    if expected.get("gender") == predicted.get("gender"):
        score += 0.05
    
    return min(1.0, score)


class ClinicalOptimizer:
    """临床信息抽取优化器"""
    
    def __init__(self, base_extractor: ClinicalExtractor):
        self.base_extractor = base_extractor
        self.optimization_results = []
    
    def optimize(self) -> ClinicalExtractor:
        """运行优化流程"""
        
        print("🚀 开始DSPy优化流程")
        print("=" * 60)
        
        # 创建训练数据
        trainset = create_training_examples()
        print(f"训练集大小: {len(trainset)} 个示例")
        
        # 评估基础模型
        print("\n📊 评估基础模型性能...")
        base_score = self._evaluate_model(self.base_extractor, trainset)
        print(f"基础模型平均得分: {base_score:.3f}")
        
        self.optimization_results.append({
            "stage": "基础模型",
            "score": base_score,
            "time": 0
        })
        
        # 配置并运行BootstrapFewShot优化
        print("\n🎯 开始BootstrapFewShot优化...")
        start_time = time.time()
        
        optimizer = dspy.BootstrapFewShot(
            metric=extraction_metric,
            max_bootstrapped_demos=5,  # 增加到5个
            max_labeled_demos=3        # 增加到3个
        )
        
        try:
            # 创建一个可优化的模块
            class OptimizableExtractor(dspy.Module):
                def __init__(self, extractor):
                    super().__init__()
                    self.extractor = extractor
                    self.analyzer = extractor.analyzer
                
                def forward(self, clinical_text, ecrf_info):
                    return self.extractor.extract(clinical_text, ecrf_info)
            
            optimizable = OptimizableExtractor(self.base_extractor)
            
            optimized_module = optimizer.compile(
                student=optimizable,
                trainset=trainset
            )
            
            optimization_time = time.time() - start_time
            
            # 评估优化后的模型
            print("\n📊 评估优化后模型性能...")
            optimized_score = self._evaluate_model(optimized_module, trainset)
            print(f"优化后模型平均得分: {optimized_score:.3f}")
            
            improvement = optimized_score - base_score
            print(f"性能变化: {improvement:+.3f}")
            print(f"优化耗时: {optimization_time:.2f}秒")
            
            self.optimization_results.append({
                "stage": "BootstrapFewShot优化",
                "score": optimized_score,
                "time": optimization_time,
                "improvement": improvement
            })
            
            # 选择更好的模型
            if optimized_score > base_score:
                print("✅ 优化成功，使用优化后的模型")
                # 将优化后的analyzer复制回原始extractor
                self.base_extractor.analyzer = optimized_module.analyzer
                final_extractor = self.base_extractor
                
                # 保存优化后的模型
                try:
                    optimized_module.save("optimized_clinical_model.json")
                    print("✅ 优化模型已保存到: optimized_clinical_model.json")
                except Exception as e:
                    print(f"⚠️ 模型保存失败: {e}")
            else:
                print("⚠️ 优化效果不佳，保持使用基础模型")
                final_extractor = self.base_extractor
            
            # 保存优化结果
            with open("optimization_results.json", "w", encoding="utf-8") as f:
                json.dump(self.optimization_results, f, ensure_ascii=False, indent=2)
            print("✅ 优化结果已保存到: optimization_results.json")
            
            return final_extractor
            
        except Exception as e:
            print(f"❌ 优化过程失败: {e}")
            return self.base_extractor
    
    def _evaluate_model(self, model, examples: List[dspy.Example]) -> float:
        """评估模型性能"""
        
        total_score = 0.0
        valid_examples = 0
        
        for i, example in enumerate(examples):
            try:
                prediction = model.forward(example.clinical_text, example.ecrf_info)
                score = extraction_metric(example, prediction)
                total_score += score
                valid_examples += 1
                print(f"  示例 {i+1} 得分: {score:.3f}")
            except Exception as e:
                print(f"  示例 {i+1} 评估失败: {e}")
                continue
        
        return total_score / valid_examples if valid_examples > 0 else 0.0


def optimize_extractor(extractor: ClinicalExtractor) -> ClinicalExtractor:
    """优化抽取器的便捷函数"""
    
    optimizer = ClinicalOptimizer(extractor)
    return optimizer.optimize()


if __name__ == "__main__":
    # 测试优化器
    from clinical_extractor import ClinicalExtractor, LLMConfig
    
    print("🎯 DSPy优化器测试")
    print("=" * 50)
    
    # 创建基础抽取器
    config = LLMConfig()
    extractor = ClinicalExtractor(config)
    
    # 运行优化
    optimized_extractor = optimize_extractor(extractor)
    
    print("\n🎉 优化测试完成！")
