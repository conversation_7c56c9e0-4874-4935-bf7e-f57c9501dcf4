"""
高级临床信息抽取器
专门处理复杂的真实临床试验案例
"""

import dspy
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


# 配置LLM模型
lm = dspy.LM("openai/DeepSeek-V3", 
             api_key="sP8TpUbNGBZfEYI", 
             api_base="https://llmaas.hengrui.com/v1",
             cache=False)
dspy.configure(lm=lm)


class AdvancedClinicalAnalyzer(dspy.Signature):
    """高级临床试验文本分析器

    专门处理复杂的真实临床试验案例，能够：
    1. 识别复合条件（如年龄范围、BMI范围）
    2. 处理性别特异性规则（如男女不同的体重要求）
    3. 识别定性检查（如病史、过敏史、妊娠试验、感染标志物）
    4. 处理复杂的排除标准（如疾病史、用药史、生活习惯）
    5. 正确映射到相应的eCRF表结构

    重要原则：
    - 每个数值限制都要转换为检查规则（年龄、体重、BMI、血液指标等）
    - 性别特异性规则要分别创建
    - 定性检查要明确阳性/阴性标准（感染标志物、病史、过敏史等）
    - 复合条件要拆分为多个独立规则
    - 时间限制要转换为检查规则（如3个月内、1个月内等）
    - 生活习惯要转换为检查规则（吸烟、饮酒、饮食等）
    - 特殊评估要转换为检查规则（心理评估、疼痛敏感性等）

    请确保提取所有可以量化检查的条件，包括但不限于：
    - 人口学信息（年龄、性别）
    - 生命体征（体重、BMI）
    - 实验室检查（感染标志物、妊娠试验、药物滥用、酒精检测）
    - 病史信息（疾病史、手术史、过敏史）
    - 用药史（禁用药物、疫苗接种）
    - 生活习惯（吸烟、饮酒、饮食）
    - 心理评估（自杀意念）
    - 特殊评估（疼痛敏感性、体检异常）
    """
    
    clinical_text: str = dspy.InputField(desc="复杂的临床试验入组和排除标准文本")
    ecrf_info: str = dspy.InputField(desc="eCRF表结构信息")
    
    analysis_steps: str = dspy.OutputField(
        desc="""分析步骤，包含：
        1. 识别的所有数值限制条件
        2. 识别的性别特异性规则
        3. 识别的定性检查项目
        4. 复合条件的拆分方案
        5. 表映射策略"""
    )
    
    extracted_rules: str = dspy.OutputField(
        desc="""完整的检查规则JSON数组，必须是有效的JSON格式，例如：
        [
          {
            "id": 1,
            "tableName": "DM",
            "checkItemOID": "DM.BRTHDAT",
            "checkFieldOID": "BRTHDAT",
            "checkItemName": "年龄",
            "operator": "<",
            "numericValue": 18,
            "nonNumericValue": null,
            "gender": null,
            "unit": "岁",
            "message": "年龄<18岁，不满足入选标准"
          }
        ]
        确保涵盖所有可定量核查的条件，包括年龄、体重、BMI、妊娠试验、感染性标志物等"""
    )


class AdvancedTableMapper(dspy.Signature):
    """高级表映射器
    
    根据检查项目的特征和eCRF结构，智能选择最合适的表和字段
    """
    
    check_item: str = dspy.InputField(desc="检查项目名称和描述")
    ecrf_structure: str = dspy.InputField(desc="完整的eCRF表结构")
    
    mapping_result: str = dspy.OutputField(
        desc="""映射结果JSON格式：
        {
          "tableName": "最合适的表名",
          "checkItemOID": "检查项目OID",
          "checkFieldOID": "检查字段OID",
          "reasoning": "选择理由"
        }"""
    )


class AdvancedClinicalExtractor:
    """高级临床信息抽取器"""
    
    def __init__(self):
        self.analyzer = dspy.ChainOfThought(AdvancedClinicalAnalyzer)
        self.mapper = dspy.ChainOfThought(AdvancedTableMapper)
        
        # 扩展的检查类型映射
        self.check_type_mapping = {
            # 人口学信息
            "年龄": {"category": "demographics", "preferred_tables": ["DM", "SUBJECT"]},
            "性别": {"category": "demographics", "preferred_tables": ["DM", "SUBJECT"]},
            "体重": {"category": "vital_signs", "preferred_tables": ["VS_SUB", "DM"]},
            "身高": {"category": "vital_signs", "preferred_tables": ["VS_SUB", "DM"]},
            "BMI": {"category": "vital_signs", "preferred_tables": ["VS_SUB", "DM"]},
            
            # 实验室检查
            "妊娠试验": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "乙肝表面抗原": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "丙型肝炎病毒抗体": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "梅毒": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "HIV": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "药物滥用筛查": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            "酒精呼气": {"category": "laboratory", "preferred_tables": ["LBQUAL_SUB", "LB_SUB"]},
            
            # 病史和评估
            "既往病史": {"category": "medical_history", "preferred_tables": ["MH", "MEDICAL_HISTORY"]},
            "过敏史": {"category": "medical_history", "preferred_tables": ["MH", "MEDICAL_HISTORY"]},
            "手术史": {"category": "medical_history", "preferred_tables": ["MH", "MEDICAL_HISTORY"]},
            "用药史": {"category": "concomitant_meds", "preferred_tables": ["CM", "CONMED"]},
            
            # 心理评估
            "自杀意念": {"category": "psychology", "preferred_tables": ["PSYCH", "MENTAL_STATUS"]},
            "心理状态": {"category": "psychology", "preferred_tables": ["PSYCH", "MENTAL_STATUS"]},
            
            # 特殊评估
            "疼痛试验": {"category": "special_assessment", "preferred_tables": ["SPECIAL", "ASSESSMENT"]},
            "体检": {"category": "physical_exam", "preferred_tables": ["PE", "PHYSICAL_EXAM"]},
        }
    
    def extract_with_analysis(self, clinical_text: str, ecrf_info: str) -> Dict[str, Any]:
        """带分析步骤的抽取"""
        
        # 使用高级分析器
        result = self.analyzer(
            clinical_text=clinical_text,
            ecrf_info=ecrf_info
        )
        
        try:
            # 解析分析步骤
            analysis_steps = result.analysis_steps
            
            # 解析抽取规则
            rules_data = json.loads(result.extracted_rules)
            if not isinstance(rules_data, list):
                rules_data = [rules_data] if rules_data else []
            
            # 后处理：优化表映射
            optimized_rules = []
            for rule in rules_data:
                optimized_rule = self._optimize_rule_mapping(rule, ecrf_info)
                if optimized_rule:
                    optimized_rules.append(optimized_rule)
            
            return {
                "analysis_steps": analysis_steps,
                "rules": optimized_rules,
                "total_rules": len(optimized_rules)
            }
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {
                "analysis_steps": result.analysis_steps,
                "rules": [],
                "total_rules": 0,
                "error": str(e)
            }
    
    def _optimize_rule_mapping(self, rule: Dict[str, Any], ecrf_info: str) -> Optional[Dict[str, Any]]:
        """优化规则的表映射"""
        
        check_item = rule.get("checkItemName", "")
        if not check_item:
            return None
        
        # 使用映射器优化表选择
        try:
            mapping_result = self.mapper(
                check_item=f"{check_item}: {rule.get('message', '')}",
                ecrf_structure=ecrf_info
            )

            # 尝试解析JSON，如果失败则使用原始规则
            try:
                mapping_data = json.loads(mapping_result.mapping_result)

                # 更新规则的表映射
                rule.update({
                    "tableName": mapping_data.get("tableName", rule.get("tableName")),
                    "checkItemOID": mapping_data.get("checkItemOID", rule.get("checkItemOID")),
                    "checkFieldOID": mapping_data.get("checkFieldOID", rule.get("checkFieldOID"))
                })
            except json.JSONDecodeError:
                # JSON解析失败，使用基础映射逻辑
                optimized_rule = self._basic_table_mapping(rule, ecrf_info)
                if optimized_rule:
                    rule = optimized_rule

            return rule

        except Exception as e:
            print(f"表映射优化失败: {e}")
            return rule

    def _basic_table_mapping(self, rule: Dict[str, Any], ecrf_info: str) -> Optional[Dict[str, Any]]:
        """基础表映射逻辑"""

        check_item = rule.get("checkItemName", "").lower()

        # 解析eCRF结构
        ecrf_lines = ecrf_info.strip().split('\n')
        available_tables = {}

        for line in ecrf_lines:
            if '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 4:
                    table_desc, table_name, field_desc, field_name = parts[:4]
                    if table_name not in available_tables:
                        available_tables[table_name] = []
                    available_tables[table_name].append({
                        "field_name": field_name,
                        "field_desc": field_desc,
                        "table_desc": table_desc
                    })

        # 基于检查项目选择最合适的表
        best_table = None
        best_field = None

        # 检查类型映射
        if check_item in self.check_type_mapping:
            preferred_tables = self.check_type_mapping[check_item]["preferred_tables"]
            for table in preferred_tables:
                if table in available_tables:
                    best_table = table
                    # 选择合适的字段
                    for field_info in available_tables[table]:
                        if "结果" in field_info["field_desc"] or "ORRES" in field_info["field_name"]:
                            best_field = field_info["field_name"]
                            break
                    if best_field:
                        break

        # 如果没有找到，使用关键词匹配
        if not best_table:
            for table_name, fields in available_tables.items():
                table_desc = fields[0]["table_desc"] if fields else ""

                # 关键词匹配
                if any(keyword in check_item for keyword in ["年龄", "性别", "出生"]):
                    if "人口学" in table_desc or "DM" in table_name:
                        best_table = table_name
                        best_field = "BRTHDAT" if "年龄" in check_item else "SEX"
                        break
                elif any(keyword in check_item for keyword in ["体重", "身高", "BMI", "血压"]):
                    if "生命体征" in table_desc or "VS" in table_name:
                        best_table = table_name
                        best_field = "VSORRES"
                        break
                elif any(keyword in check_item for keyword in ["妊娠", "乙肝", "HIV", "梅毒"]):
                    if "实验室" in table_desc and ("定性" in table_desc or "QUAL" in table_name):
                        best_table = table_name
                        best_field = "LBORRES"
                        break

        # 更新规则
        if best_table:
            rule.update({
                "tableName": best_table,
                "checkItemOID": f"{best_table}.{best_field}" if best_field else rule.get("checkItemOID"),
                "checkFieldOID": best_field if best_field else rule.get("checkFieldOID")
            })

        return rule
    
    def extract_comprehensive_rules(self, clinical_text: str, ecrf_info: str) -> List[Dict[str, Any]]:
        """综合抽取规则（简化接口）"""
        
        result = self.extract_with_analysis(clinical_text, ecrf_info)
        return result.get("rules", [])


def test_advanced_extractor():
    """测试高级抽取器"""
    
    print("🎯 测试高级临床信息抽取器")
    print("=" * 60)
    
    # 创建高级抽取器
    extractor = AdvancedClinicalExtractor()
    
    # 使用用户提供的复杂案例（简化版本用于测试）
    test_text = """入选标准:
1. 年龄介于18~55周岁（含边界值），男女均可；
2. 筛选期男性体重≥50 kg，女性体重≥45 kg，BMI介于19~28 kg/m2（含边界值）；
3. 有生育能力的女性受试者必须在筛选期进行血清妊娠试验，且结果为阴性；

排除标准:
1. 既往有尖端扭转型室性心动过速病史者；
2. 乙肝表面抗原、丙型肝炎病毒抗体、梅毒或HIV抗体检查有一项或一项以上呈阳性者；
3. 筛选期经心理状态评估判定为有自杀意念者；"""
    
    ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选
生命体征明细\tVS_SUB\t检查项\tVSTEST\t文本
生命体征明细\tVS_SUB\t结果\tVSORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选
病史明细\tMH\t病史描述\tMHTERM\t文本
病史明细\tMH\t是否存在\tMHOCCUR\t单选
心理评估\tPSYCH\t评估项目\tPSTEST\t文本
心理评估\tPSYCH\t评估结果\tPSORRES\t单选"""
    
    # 执行带分析的抽取
    result = extractor.extract_with_analysis(test_text, ecrf_info)
    
    print(f"\n📋 分析步骤:")
    print(result.get("analysis_steps", "无"))
    
    print(f"\n✅ 成功提取了 {result['total_rules']} 条规则:")
    
    for i, rule in enumerate(result["rules"][:10], 1):  # 显示前10条
        print(f"\n规则 {i}:")
        print(f"  检查项: {rule.get('checkItemName', 'N/A')}")
        print(f"  表名: {rule.get('tableName', 'N/A')}")
        print(f"  字段: {rule.get('checkFieldOID', 'N/A')}")
        print(f"  操作符: {rule.get('operator', 'N/A')}")
        print(f"  数值: {rule.get('numericValue', rule.get('nonNumericValue', 'N/A'))}")
        print(f"  性别: {rule.get('gender', 'N/A')}")
        print(f"  单位: {rule.get('unit', 'N/A')}")
    
    # 保存结果
    with open("advanced_extraction_results.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 advanced_extraction_results.json")
    
    return result


if __name__ == "__main__":
    test_advanced_extractor()
