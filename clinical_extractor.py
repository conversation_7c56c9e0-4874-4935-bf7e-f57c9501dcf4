"""
临床试验标准化信息抽取系统
基于DSPy框架的生产就绪版本
"""

import dspy
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class LLMConfig:
    """LLM配置类"""
    model_name: str = "openai/DeepSeek-V3"
    api_key: str = "sP8TpUbNGBZfEYI"
    api_base: str = "https://llmaas.hengrui.com/v1"
    cache: bool = False


class ClinicalAnalyzer(dspy.Signature):
    """临床试验文本分析器
    
    从临床试验的入组和排除标准中提取结构化的检查规则。
    能够自动识别和处理各种检查类型，不依赖预定义映射。
    """
    
    clinical_text: str = dspy.InputField(desc="临床试验的入组和排除标准文本")
    ecrf_info: str = dspy.InputField(desc="eCRF表结构信息，包含表名、字段名、变量类型等")
    
    extracted_rules: str = dspy.OutputField(
        desc="""基于eCRF结构自动提取的检查规则JSON数组。

分析步骤：
1. 解析eCRF表结构，理解各表的用途和字段含义
2. 从临床文本中识别所有检查项目和限制条件
3. 智能匹配检查项目到合适的eCRF表和字段
4. 生成完整的检查规则

输出格式：
[
  {
    "id": 规则编号,
    "tableName": "从eCRF中选择的最合适表名",
    "checkItemOID": "表名.主要字段名",
    "checkFieldOID": "具体的检查字段名",
    "checkItemName": "检查项目的标准化名称",
    "operator": "操作符（<, >, ≤, ≥, =, !=）",
    "numericValue": 数值（如果适用）,
    "nonNumericValue": "非数值（如果适用）",
    "gender": "适用性别（Male/Female/null）",
    "unit": "单位",
    "message": "标准化错误消息"
  }
]

重要原则：
- 优先使用eCRF中实际存在的表名和字段名
- 对于未明确匹配的检查项，选择最相关的通用表（如LB_SUB用于实验室检查）
- 保持检查项目名称的原始含义，避免过度转换
- 确保操作符逻辑正确（入选标准的限制转换为排除条件）"""
    )


class ClinicalExtractor:
    """临床信息抽取器"""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """初始化抽取器
        
        Args:
            config: LLM配置，如果为None则使用默认配置
        """
        if config is None:
            config = LLMConfig()
        
        # 配置LLM
        lm = dspy.LM(
            model=config.model_name,
            api_key=config.api_key,
            api_base=config.api_base,
            cache=config.cache
        )
        dspy.configure(lm=lm)
        
        # 初始化分析器
        self.analyzer = dspy.ChainOfThought(ClinicalAnalyzer)
        
        # 检查类型模式（用于辅助识别）
        self.check_patterns = {
            "age": ["年龄", "age", "岁"],
            "laboratory": ["ANC", "PLT", "Hgb", "AST", "ALT", "肌酐", "胆红素", "血小板", "血红蛋白"],
            "ecg": ["QTc", "心电图", "ECG", "心律"],
            "echo": ["LVEF", "超声", "心动图"],
            "virology": ["HBV", "HCV", "HIV", "病毒", "DNA", "RNA"],
            "performance": ["ECOG", "PS", "体能", "评分"],
            "vital_signs": ["血压", "体温", "脉搏", "呼吸"],
            "pregnancy": ["妊娠", "怀孕", "hCG"]
        }
    
    def parse_ecrf_structure(self, ecrf_info: str) -> Dict[str, Dict[str, Any]]:
        """解析eCRF结构信息"""
        
        tables = {}
        lines = ecrf_info.strip().split('\n')
        
        for line in lines:
            if '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 4:
                    table_desc = parts[0]
                    table_name = parts[1]
                    field_desc = parts[2]
                    field_name = parts[3]
                    field_type = parts[4] if len(parts) > 4 else "unknown"
                    
                    if table_name not in tables:
                        tables[table_name] = {
                            "description": table_desc,
                            "fields": {},
                            "field_types": {}
                        }
                    
                    tables[table_name]["fields"][field_desc] = field_name
                    tables[table_name]["field_types"][field_name] = field_type
        
        return tables
    
    def identify_check_category(self, check_item: str) -> str:
        """识别检查项目的类别"""
        
        check_item_lower = check_item.lower()
        
        for category, keywords in self.check_patterns.items():
            for keyword in keywords:
                if keyword.lower() in check_item_lower:
                    return category
        
        return "laboratory"  # 默认归类为实验室检查
    
    def find_best_table_match(self, check_item: str, tables: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
        """为检查项目找到最佳的表匹配"""
        
        category = self.identify_check_category(check_item)
        
        # 基于类别的表优先级
        table_priorities = {
            "age": ["DM", "SUBJECT"],
            "laboratory": ["LB_SUB", "LBVT_SUB", "LBQUAL_SUB", "LB", "LBNCS_SUB"],
            "ecg": ["EG_SUB", "EG"],
            "echo": ["CVLVEF", "CV"],
            "virology": ["LBVT_SUB", "LBQUAL_SUB", "LB_SUB"],
            "performance": ["RSECOG", "RS"],
            "vital_signs": ["VS_SUB", "VS"],
            "pregnancy": ["LBQUAL_SUB", "LB_SUB"]
        }
        
        # 获取该类别的优先表列表
        priority_tables = table_priorities.get(category, ["LB_SUB"])
        
        # 寻找最佳匹配
        for table_name in priority_tables:
            if table_name in tables:
                table_info = tables[table_name]
                
                # 优先选择结果字段
                result_fields = ["LBORRES", "EGORRES", "CVORRES", "RSORRES", "VSORRES"]
                for field in result_fields:
                    if field in table_info.get("field_types", {}):
                        return {
                            "tableName": table_name,
                            "checkItemOID": f"{table_name}.{list(table_info['fields'].values())[0]}",
                            "checkFieldOID": field
                        }
                
                # 如果没有找到结果字段，使用第一个可用字段
                if table_info["fields"]:
                    first_field = list(table_info["fields"].values())[0]
                    return {
                        "tableName": table_name,
                        "checkItemOID": f"{table_name}.{first_field}",
                        "checkFieldOID": first_field
                    }
        
        # 默认使用实验室检查表
        return {
            "tableName": "LB_SUB",
            "checkItemOID": "LB_SUB.LBTEST",
            "checkFieldOID": "LBORRES"
        }
    
    def forward(self, clinical_text: str, ecrf_info: str) -> List[Dict[str, Any]]:
        """DSPy兼容的前向传播方法"""
        return self.extract(clinical_text, ecrf_info)

    def extract(self, clinical_text: str, ecrf_info: str) -> List[Dict[str, Any]]:
        """从临床文本中抽取检查规则

        Args:
            clinical_text: 临床试验的入组和排除标准文本
            ecrf_info: eCRF表结构信息

        Returns:
            提取的检查规则列表
        """
        
        try:
            # 解析eCRF结构
            tables = self.parse_ecrf_structure(ecrf_info)
            
            # 使用分析器提取规则
            analysis_result = self.analyzer(
                clinical_text=clinical_text,
                ecrf_info=ecrf_info
            )
            
            # 解析JSON结果
            rules_data = json.loads(analysis_result.extracted_rules)
            if not isinstance(rules_data, list):
                rules_data = [rules_data] if rules_data else []
            
            # 后处理：确保表映射的准确性
            processed_rules = []
            for rule in rules_data:
                processed_rule = self._post_process_rule(rule, tables)
                if processed_rule and self._validate_rule(processed_rule):
                    processed_rules.append(processed_rule)
            
            return processed_rules
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return []
        except Exception as e:
            print(f"抽取过程中出现错误: {e}")
            return []
    
    def _post_process_rule(self, rule: Dict[str, Any], tables: Dict[str, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """后处理规则，确保表映射的准确性"""
        
        if not isinstance(rule, dict):
            return None
        
        check_item = rule.get("checkItemName", "")
        if not check_item:
            return None
        
        # 如果规则中的表名不在eCRF中，重新匹配
        table_name = rule.get("tableName", "")
        if table_name not in tables:
            best_match = self.find_best_table_match(check_item, tables)
            rule.update(best_match)
        
        # 确保必要字段存在
        if not rule.get("checkItemOID"):
            rule["checkItemOID"] = f"{rule['tableName']}.{check_item}"
        
        if not rule.get("checkFieldOID"):
            rule["checkFieldOID"] = "RESULT"
        
        # 标准化操作符
        operator = rule.get("operator", "")
        operator_mapping = {"≥": ">=", "≤": "<=", "＞": ">", "＜": "<"}
        if operator in operator_mapping:
            rule["operator"] = operator_mapping[operator]
        
        return rule
    
    def _validate_rule(self, rule: Dict[str, Any]) -> bool:
        """验证规则的完整性"""
        
        required_fields = ["tableName", "checkItemOID", "checkFieldOID", "checkItemName", "operator"]
        
        for field in required_fields:
            if field not in rule or not rule[field]:
                return False
        
        # 验证操作符
        valid_operators = ["<", ">", "<=", ">=", "=", "!="]
        if rule["operator"] not in valid_operators:
            return False
        
        # 验证数值和非数值字段
        if rule.get("numericValue") is None and rule.get("nonNumericValue") is None:
            return False
        
        return True
    
    def extract_from_file(self, clinical_file: str, ecrf_file: str) -> List[Dict[str, Any]]:
        """从文件中抽取检查规则
        
        Args:
            clinical_file: 临床文本文件路径
            ecrf_file: eCRF信息文件路径
            
        Returns:
            提取的检查规则列表
        """
        
        try:
            with open(clinical_file, 'r', encoding='utf-8') as f:
                clinical_text = f.read()
            
            with open(ecrf_file, 'r', encoding='utf-8') as f:
                ecrf_info = f.read()
            
            return self.extract(clinical_text, ecrf_info)
            
        except Exception as e:
            print(f"文件读取错误: {e}")
            return []
    
    def save_results(self, rules: List[Dict[str, Any]], output_file: str):
        """保存结果到文件
        
        Args:
            rules: 检查规则列表
            output_file: 输出文件路径
        """
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(rules, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")


def update_llm_config(new_config: LLMConfig):
    """更新LLM配置
    
    Args:
        new_config: 新的LLM配置
    """
    
    # 保存配置到文件
    config_dict = {
        "model_name": new_config.model_name,
        "api_key": new_config.api_key,
        "api_base": new_config.api_base,
        "cache": new_config.cache
    }
    
    with open("llm_config.json", "w", encoding="utf-8") as f:
        json.dump(config_dict, f, ensure_ascii=False, indent=2)
    
    print("✅ LLM配置已更新")


def load_llm_config() -> LLMConfig:
    """加载LLM配置
    
    Returns:
        LLM配置对象
    """
    
    try:
        with open("llm_config.json", "r", encoding="utf-8") as f:
            config_dict = json.load(f)
        
        return LLMConfig(
            model_name=config_dict.get("model_name", "openai/DeepSeek-V3"),
            api_key=config_dict.get("api_key", ""),
            api_base=config_dict.get("api_base", ""),
            cache=config_dict.get("cache", False)
        )
    except:
        # 如果配置文件不存在，返回默认配置
        return LLMConfig()


if __name__ == "__main__":
    # 示例用法
    print("🎯 临床试验标准化信息抽取系统")
    print("=" * 50)
    
    # 创建抽取器
    extractor = ClinicalExtractor()
    
    # 示例数据
    clinical_text = """入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
ANC≥1.0×10^9/L；
排除标准:
QTc间期延长（男性>450ms、女性>470ms）；"""
    
    ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值"""
    
    # 执行抽取
    rules = extractor.extract(clinical_text, ecrf_info)
    
    print(f"✅ 成功提取了 {len(rules)} 条规则")
    
    # 保存结果
    extractor.save_results(rules, "extraction_results.json")
    
    print("🎉 示例执行完成！")
