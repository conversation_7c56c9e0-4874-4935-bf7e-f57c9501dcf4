{"analyzer.predict": {"traces": [], "train": [], "demos": [{"augmented": true, "clinical_text": "入选标准:\n年龄18~70周岁（含18和70周岁），性别不限；\nECOG 体能状态（Performance Status [PS]）必须为0或1分；\n排除标准:\n年龄不在18-70周岁范围内的患者；\nECOG评分>1分的患者；", "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间\nECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选", "reasoning": "1. 分析临床文本中的入选和排除标准：\n   - 年龄范围：18-70岁（包含边界值）\n   - ECOG评分：0或1分（入选标准），>1分（排除标准）\n\n2. 匹配eCRF表结构：\n   - 年龄信息应存储在DM表（人口学资料）的BRTHDAT字段（出生日期），需要计算年龄\n   - ECOG评分有专用表RSECOG，对应字段为RSORRES\n\n3. 规则转换逻辑：\n   - 入选标准转换为检查规则时需要取反（因为系统需要检查不符合入选标准的情况）\n   - 年龄检查需要转换为出生日期范围计算\n   - ECOG评分直接使用单选值检查", "extracted_rules": "[\n  {\n    \"id\": 1,\n    \"tableName\": \"DM\",\n    \"checkItemOID\": \"DM.BRTHDAT\",\n    \"checkFieldOID\": \"DM.BRTHDAT\",\n    \"checkItemName\": \"年龄\",\n    \"operator\": \"<\",\n    \"numericValue\": 18,\n    \"nonNumericValue\": null,\n    \"gender\": null,\n    \"unit\": \"岁\",\n    \"message\": \"年龄小于18岁不符合入选标准\"\n  },\n  {\n    \"id\": 2,\n    \"tableName\": \"DM\",\n    \"checkItemOID\": \"DM.BRTHDAT\",\n    \"checkFieldOID\": \"DM.BRTHDAT\",\n    \"checkItemName\": \"年龄\",\n    \"operator\": \">\",\n    \"numericValue\": 70,\n    \"nonNumericValue\": null,\n    \"gender\": null,\n    \"unit\": \"岁\",\n    \"message\": \"年龄大于70岁不符合入选标准\"\n  },\n  {\n    \"id\": 3,\n    \"tableName\": \"RSECOG\",\n    \"checkItemOID\": \"RSECOG.RSORRES\",\n    \"checkFieldOID\": \"RSECOG.RSORRES\",\n    \"checkItemName\": \"ECOG评分\",\n    \"operator\": \">\",\n    \"numericValue\": 1,\n    \"nonNumericValue\": null,\n    \"gender\": null,\n    \"unit\": null,\n    \"message\": \"ECOG评分大于1分不符合入选标准\"\n  }\n]"}, {"augmented": true, "clinical_text": "入选标准:\n骨髓功能基本正常，包括：\nANC≥1.0×10^9/L（检查前14天内未使用过生长因子）；\n血小板计数≥75×10^9/L（检查前7天内未输注过血小板）；\n排除标准:\nANC<1.0×10^9/L的患者；\n血小板计数<75×10^9/L的患者；", "ecrf_info": "实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本\n实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值", "reasoning": "1. 分析临床文本发现主要涉及两个实验室检查项目：ANC(中性粒细胞绝对值)和血小板计数\n2. 两个项目都有明确的数值限制和单位(10^9/L)\n3. eCRF中LB_SUB表专门用于实验室检查，包含LBTEST(检查项)和LBORRES(结果)字段\n4. ANC和血小板计数都属于常规实验室检查，适合放在LB_SUB表中\n5. 需要将入选标准(≥)转换为排除条件(<)的逻辑转换\n6. 识别出两个项目都有时间相关的排除条件(生长因子/血小板输注)，但这些属于用药史，当前eCRF结构不支持", "extracted_rules": "[\n  {\n    \"id\": 1,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"ANC\",\n    \"operator\": \"<\",\n    \"numericValue\": 1.0,\n    \"nonNumericValue\": null,\n    \"gender\": null,\n    \"unit\": \"10^9/L\",\n    \"message\": \"ANC值低于1.0×10^9/L不符合入选标准\"\n  },\n  {\n    \"id\": 2,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"血小板计数\",\n    \"operator\": \"<\",\n    \"numericValue\": 75.0,\n    \"nonNumericValue\": null,\n    \"gender\": null,\n    \"unit\": \"10^9/L\",\n    \"message\": \"血小板计数低于75×10^9/L不符合入选标准\"\n  }\n]"}, {"augmented": true, "clinical_text": "入选标准:\n肝肾功能基本正常：\n血清肌酐≤1.5×正常参考值上限 (ULN)；\n血清总胆红素≤1.5×ULN；\n谷草转氨酶（AST）和谷丙转氨酶（ALT）≤3×ULN；\n排除标准:\n肌酐>1.5×ULN的患者；\n总胆红素>1.5×ULN的患者；\nAST或ALT>3×ULN的患者；", "ecrf_info": "实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本\n实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值", "reasoning": "1. 分析临床文本发现主要涉及肝肾功能检查，包括血清肌酐、总胆红素、AST和ALT四项指标\n2. eCRF中提供了LB_SUB表用于实验室检查，包含LBTEST(检查项)和LBORRES(结果)两个关键字段\n3. 入选标准中的\"≤\"条件需要转换为检查规则中的\">\"排除条件\n4. 所有检查项目都属于实验室检查，适合映射到LB_SUB表\n5. 需要为每个检查项目创建两条规则：一条入选规则和一条排除规则\n6. 所有检查项目都使用ULN(正常参考值上限)作为比较基准", "extracted_rules": "[\n  {\n    \"id\": 1,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"血清肌酐\",\n    \"operator\": \">\",\n    \"numericValue\": 1.5,\n    \"nonNumericValue\": \"ULN\",\n    \"gender\": null,\n    \"unit\": null,\n    \"message\": \"血清肌酐值超过1.5×ULN\"\n  },\n  {\n    \"id\": 2,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"血清总胆红素\",\n    \"operator\": \">\",\n    \"numericValue\": 1.5,\n    \"nonNumericValue\": \"ULN\",\n    \"gender\": null,\n    \"unit\": null,\n    \"message\": \"血清总胆红素值超过1.5×ULN\"\n  },\n  {\n    \"id\": 3,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"谷草转氨酶(AST)\",\n    \"operator\": \">\",\n    \"numericValue\": 3,\n    \"nonNumericValue\": \"ULN\",\n    \"gender\": null,\n    \"unit\": null,\n    \"message\": \"AST值超过3×ULN\"\n  },\n  {\n    \"id\": 4,\n    \"tableName\": \"LB_SUB\",\n    \"checkItemOID\": \"LB_SUB.LBTEST\",\n    \"checkFieldOID\": \"LB_SUB.LBORRES\",\n    \"checkItemName\": \"谷丙转氨酶(ALT)\",\n    \"operator\": \">\",\n    \"numericValue\": 3,\n    \"nonNumericValue\": \"ULN\",\n    \"gender\": null,\n    \"unit\": null,\n    \"message\": \"ALT值超过3×ULN\"\n  }\n]"}], "signature": {"instructions": "灵活的临床试验文本分析器\n\n能够自动识别和处理未知的检查类型，不依赖预定义的映射表。\n\n核心能力：\n1. 自动识别检查项目类型（实验室、心电图、影像学等）\n2. 智能匹配eCRF表结构\n3. 动态生成表名和字段映射\n4. 处理复杂的数值表达式和单位\n5. 识别性别特异性规则\n\n分析策略：\n- 从eCRF信息中学习表结构模式\n- 基于检查项目名称推断所属类别\n- 自动匹配最合适的表名和字段\n- 生成标准化的检查规则", "fields": [{"prefix": "Clinical Text:", "description": "临床试验的入组和排除标准文本"}, {"prefix": "Ecrf Info:", "description": "eCRF表结构信息，包含表名、字段名、变量类型等"}, {"prefix": "Reasoning: Let's think step by step in order to", "description": "${reasoning}"}, {"prefix": "Extracted Rules:", "description": "基于eCRF结构自动提取的检查规则JSON数组。\n\n分析步骤：\n1. 解析eCRF表结构，理解各表的用途和字段含义\n2. 从临床文本中识别所有检查项目和限制条件\n3. 智能匹配检查项目到合适的eCRF表和字段\n4. 生成完整的检查规则\n\n输出格式：\n[\n  {\n    \"id\": 规则编号,\n    \"tableName\": \"从eCRF中选择的最合适表名\",\n    \"checkItemOID\": \"表名.主要字段名\",\n    \"checkFieldOID\": \"具体的检查字段名\",\n    \"checkItemName\": \"检查项目的标准化名称\",\n    \"operator\": \"操作符（<, >, ≤, ≥, =, !=）\",\n    \"numericValue\": 数值（如果适用）,\n    \"nonNumericValue\": \"非数值（如果适用）\",\n    \"gender\": \"适用性别（Male/Female/null）\",\n    \"unit\": \"单位\",\n    \"message\": \"标准化错误消息\"\n  }\n]\n\n重要原则：\n- 优先使用eCRF中实际存在的表名和字段名\n- 对于未明确匹配的检查项，选择最相关的通用表（如LB_SUB用于实验室检查）\n- 保持检查项目名称的原始含义，避免过度转换\n- 确保操作符逻辑正确（入选标准的限制转换为排除条件）"}]}, "lm": null}, "table_matcher.predict": {"traces": [], "train": [], "demos": [], "signature": {"instructions": "灵活的表匹配器\n\n根据检查项目的特征，自动匹配到最合适的eCRF表", "fields": [{"prefix": "Check Item:", "description": "检查项目名称"}, {"prefix": "Ecrf Tables:", "description": "可用的eCRF表列表及其描述"}, {"prefix": "Reasoning: Let's think step by step in order to", "description": "${reasoning}"}, {"prefix": "Matched Table:", "description": "最匹配的表信息，JSON格式：\n        {\n          \"tableName\": \"表名\",\n          \"fieldName\": \"字段名\", \n          \"reasoning\": \"匹配理由\"\n        }"}]}, "lm": null}, "metadata": {"dependency_versions": {"python": "3.11", "dspy": "2.6.27", "cloudpickle": "3.1"}}}