[{"name": "年龄_ECOG评分", "clinical_text": "入选标准:\n年龄18~70周岁，性别不限；\nECOG评分0-1分；\n排除标准:\n年龄不在18-70周岁范围内的患者；\nECOG评分>1分的患者；", "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间\nECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选", "expected_rules": [{"id": 1, "tableName": "DM", "checkItemOID": "DM.BRTHDAT", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": "<", "numericValue": 18, "nonNumericValue": null, "gender": null, "unit": "周岁", "message": "年龄<18周岁，不满足入选标准，请核实"}, {"id": 2, "tableName": "DM", "checkItemOID": "DM.BRTHDAT", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": ">", "numericValue": 70, "nonNumericValue": null, "gender": null, "unit": "周岁", "message": "年龄>70周岁，不满足入选标准，请核实"}, {"id": 3, "tableName": "RSECOG", "checkItemOID": "RSECOG.RSORRES", "checkFieldOID": "RSORRES", "checkItemName": "ECOG评分", "operator": ">", "numericValue": 1, "nonNumericValue": null, "gender": null, "unit": null, "message": "ECOG评分>1分，满足排除标准，请核实"}], "category": "基础检查", "description": "年龄范围和ECOG评分的基本检查", "created_at": "2025-07-31T16:52:38.500906"}, {"name": "实验室检查_血常规", "clinical_text": "入选标准:\n骨髓功能基本正常：\nANC≥1.0×10^9/L；\n血小板计数≥75×10^9/L；\n排除标准:\nANC<1.0×10^9/L的患者；\n血小板计数<75×10^9/L的患者；", "ecrf_info": "实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本\n实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值", "expected_rules": [{"id": 1, "tableName": "LB_SUB", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LBORRES", "checkItemName": "ANC", "operator": "<", "numericValue": 1.0, "nonNumericValue": null, "gender": null, "unit": "10^9/L", "message": "ANC<1.0×10^9/L，不满足入选标准，请核实"}, {"id": 2, "tableName": "LB_SUB", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LBORRES", "checkItemName": "血小板计数", "operator": "<", "numericValue": 75, "nonNumericValue": null, "gender": null, "unit": "10^9/L", "message": "血小板计数<75×10^9/L，不满足入选标准，请核实"}], "category": "实验室检查", "description": "血常规相关的实验室检查标准", "created_at": "2025-07-31T16:52:38.500906"}, {"name": "心电图_QTc间期", "clinical_text": "排除标准:\n心电图QTc间期延长（男性>450ms、女性>470ms）；", "ecrf_info": "12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本\n12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值", "expected_rules": [{"id": 1, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 450, "nonNumericValue": null, "gender": "Male", "unit": "ms", "message": "男性QTc间期>450ms，满足排除标准，请核实"}, {"id": 2, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 470, "nonNumericValue": null, "gender": "Female", "unit": "ms", "message": "女性QTc间期>470ms，满足排除标准，请核实"}], "category": "心电图检查", "description": "QTc间期的性别特异性检查", "created_at": "2025-07-31T16:52:38.500906"}]