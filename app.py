"""
临床试验标准化信息抽取系统 - 主程序
简化的生产就绪版本
"""

import argparse
import json
from clinical_extractor import ClinicalExtractor, LLMConfig, update_llm_config, load_llm_config
from optimizer import optimize_extractor
from training_manager import interactive_training_manager
from advanced_extractor import AdvancedClinicalExtractor


def interactive_mode():
    """交互模式 - 使用高级抽取器"""

    print("\n🎯 交互模式（高级抽取）")
    print("您可以输入临床文本和eCRF信息进行实时抽取")
    print("输入 'quit' 退出")
    print("=" * 60)

    # 使用高级抽取器
    extractor = AdvancedClinicalExtractor()
    
    while True:
        print("\n请输入临床文本:")
        clinical_text = input().strip()
        
        if clinical_text.lower() == 'quit':
            break
        
        print("\n请输入eCRF信息 (可选，按回车使用默认):")
        ecrf_input = input().strip()
        
        if not ecrf_input:
            # 使用默认的eCRF信息
            ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值
病毒学定量检查明细\tLBVT_SUB\t检查项\tLBTEST\t文本
病毒学定量检查明细\tLBVT_SUB\t检测值\tLBORRES\t数值"""
        else:
            ecrf_info = ecrf_input
        
        # 执行高级抽取
        print("\n🔄 正在进行高级分析和抽取...")
        result = extractor.extract_with_analysis(clinical_text, ecrf_info)
        rules = result.get("rules", [])
        
        # 显示分析步骤
        print(f"\n📋 分析步骤:")
        print(result.get("analysis_steps", "无"))

        # 显示抽取结果
        print(f"\n✅ 成功提取了 {len(rules)} 条规则:")
        for i, rule in enumerate(rules[:8], 1):  # 显示前8条
            print(f"\n规则 {i}:")
            print(f"  检查项: {rule.get('checkItemName', 'N/A')}")
            print(f"  表名: {rule.get('tableName', 'N/A')}")
            print(f"  字段: {rule.get('checkFieldOID', 'N/A')}")
            print(f"  操作符: {rule.get('operator', 'N/A')}")
            print(f"  数值: {rule.get('numericValue', rule.get('nonNumericValue', 'N/A'))}")
            print(f"  性别: {rule.get('gender', 'N/A')}")
            print(f"  单位: {rule.get('unit', 'N/A')}")

        if len(rules) > 8:
            print(f"\n... 还有 {len(rules) - 8} 条规则未显示")
        
        # 询问是否保存
        save_choice = input("\n是否保存结果? (y/n): ").lower()
        if save_choice == 'y':
            filename = input("请输入文件名 (默认: interactive_results.json): ").strip()
            if not filename:
                filename = "interactive_results.json"

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {filename}")


def demo_mode():
    """演示模式 - 使用高级抽取器"""

    print("\n🎯 演示模式（高级抽取）")
    print("=" * 60)

    # 使用高级抽取器
    extractor = AdvancedClinicalExtractor()
    
    # 演示案例1：标准临床试验
    print("\n📋 演示案例1: 标准临床试验")
    standard_text = """入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
ANC≥1.0×10^9/L；
血小板计数≥75×10^9/L；
排除标准:
QTc间期延长（男性>450ms、女性>470ms）；
HBV DNA≥2×10³ IU/mL；"""
    
    standard_ecrf = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值
病毒学定量检查明细\tLBVT_SUB\t检查项\tLBTEST\t文本
病毒学定量检查明细\tLBVT_SUB\t检测值\tLBORRES\t数值"""
    
    rules1 = extractor.extract_comprehensive_rules(standard_text, standard_ecrf)
    print(f"✅ 提取了 {len(rules1)} 条标准规则")
    
    # 演示案例2：未知检查类型
    print("\n📋 演示案例2: 未知检查类型")
    novel_text = """入选标准:
新型生物标志物ABC≥100 ng/mL；
特殊免疫指标XYZ阴性；
实验性评分DEF≤5分；
排除标准:
罕见遗传标记GHI阳性；
未知病毒JKL RNA≥1000 copies/mL；"""
    
    novel_ecrf = """生物标志物检查\tBIOMAR\t标志物名称\tBMTEST\t文本
生物标志物检查\tBIOMAR\t检测结果\tBMORRES\t数值
免疫学检查\tIMMUNO\t检查项目\tIMTEST\t文本
免疫学检查\tIMMUNO\t检测结果\tIMORRES\t单选
特殊评分\tSPECIAL\t评分项目\tSPTEST\t文本
特殊评分\tSPECIAL\t评分结果\tSPORRES\t数值
遗传学检查\tGENET\t基因标记\tGNTEST\t文本
遗传学检查\tGENET\t检测结果\tGNORRES\t单选"""
    
    rules2 = extractor.extract_comprehensive_rules(novel_text, novel_ecrf)
    print(f"✅ 提取了 {len(rules2)} 条新型规则")
    
    # 保存演示结果
    all_demo_rules = rules1 + rules2
    with open("demo_results.json", "w", encoding="utf-8") as f:
        import json
        json.dump(all_demo_rules, f, ensure_ascii=False, indent=2)
    print("✅ 结果已保存到: demo_results.json")
    
    print(f"\n🎉 演示完成！共展示了系统处理 {len(all_demo_rules)} 条规则的能力")


def config_mode():
    """配置模式"""
    
    print("\n🎯 LLM配置模式")
    print("=" * 60)
    
    # 显示当前配置
    current_config = load_llm_config()
    print(f"\n当前配置:")
    print(f"  模型名称: {current_config.model_name}")
    print(f"  API密钥: {current_config.api_key[:10]}..." if current_config.api_key else "  API密钥: 未设置")
    print(f"  API地址: {current_config.api_base}")
    print(f"  缓存: {current_config.cache}")
    
    # 询问是否更新
    update_choice = input("\n是否更新配置? (y/n): ").lower()
    if update_choice != 'y':
        return
    
    # 获取新配置
    print("\n请输入新的配置信息 (按回车保持当前值):")
    
    model_name = input(f"模型名称 [{current_config.model_name}]: ").strip()
    if not model_name:
        model_name = current_config.model_name
    
    api_key = input(f"API密钥 [当前已设置]: ").strip()
    if not api_key:
        api_key = current_config.api_key
    
    api_base = input(f"API地址 [{current_config.api_base}]: ").strip()
    if not api_base:
        api_base = current_config.api_base
    
    cache_input = input(f"启用缓存 [{current_config.cache}] (y/n): ").strip().lower()
    if cache_input in ['y', 'yes']:
        cache = True
    elif cache_input in ['n', 'no']:
        cache = False
    else:
        cache = current_config.cache
    
    # 更新配置
    new_config = LLMConfig(
        model_name=model_name,
        api_key=api_key,
        api_base=api_base,
        cache=cache
    )
    
    update_llm_config(new_config)
    print("\n✅ 配置更新完成！")


def optimize_mode():
    """优化模式"""

    print("\n🎯 模型优化模式")
    print("=" * 60)

    # 加载配置
    config = load_llm_config()
    extractor = ClinicalExtractor(config)

    print("开始优化模型，这可能需要几分钟时间...")

    # 运行优化
    optimized_extractor = optimize_extractor(extractor)

    # 测试优化后的模型
    print("\n🧪 测试优化后的模型")
    test_text = """入选标准:
年龄18~70周岁；
ECOG评分0-1分；
ANC≥1.0×10^9/L；
排除标准:
QTc间期延长（男性>450ms、女性>470ms）；"""

    test_ecrf = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值"""

    rules = optimized_extractor.extract(test_text, test_ecrf)
    print(f"✅ 优化后模型测试成功，提取了 {len(rules)} 条规则")

    # 保存测试结果
    optimized_extractor.save_results(rules, "optimized_test_results.json")

    print("\n🎉 模型优化完成！")





def main():
    """主函数"""
    
    print("🎯 临床试验标准化信息抽取系统")
    print("基于DSPy框架的生产就绪版本")
    print("=" * 80)
    
    parser = argparse.ArgumentParser(description="临床信息抽取系统")
    parser.add_argument("--mode", choices=["demo", "interactive", "extract", "config", "optimize", "training"],
                       default="demo", help="运行模式")
    parser.add_argument("--input", type=str, help="输入文件路径")
    parser.add_argument("--ecrf", type=str, help="eCRF文件路径")
    parser.add_argument("--output", type=str, default="results.json", help="输出文件路径")
    
    args = parser.parse_args()
    
    if args.mode == "demo":
        demo_mode()
    
    elif args.mode == "interactive":
        interactive_mode()
    
    elif args.mode == "config":
        config_mode()

    elif args.mode == "optimize":
        optimize_mode()

    elif args.mode == "training":
        interactive_training_manager()
    
    elif args.mode == "extract":
        if not args.input or not args.ecrf:
            print("❌ 抽取模式需要提供 --input 和 --ecrf 参数")
            return
        
        # 使用高级抽取器
        extractor = AdvancedClinicalExtractor()

        # 从文件读取内容
        print(f"📄 从文件抽取: {args.input}")
        try:
            with open(args.input, 'r', encoding='utf-8') as f:
                clinical_text = f.read()
            with open(args.ecrf, 'r', encoding='utf-8') as f:
                ecrf_info = f.read()

            # 执行高级抽取
            result = extractor.extract_with_analysis(clinical_text, ecrf_info)
            rules = result.get("rules", [])

            if rules:
                print(f"✅ 成功提取了 {len(rules)} 条规则")
                with open(args.output, "w", encoding="utf-8") as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"✅ 结果已保存到: {args.output}")
            else:
                print("❌ 抽取失败或未找到规则")

        except FileNotFoundError as e:
            print(f"❌ 文件未找到: {e}")
        except Exception as e:
            print(f"❌ 抽取过程出错: {e}")
    
    print("\n" + "=" * 80)
    print("🎉 程序执行完成！")
    print("=" * 80)


if __name__ == "__main__":
    main()
