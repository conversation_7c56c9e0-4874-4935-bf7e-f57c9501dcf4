[{"id": 1, "tableName": "DM", "checkItemOID": "DM.BRTHDAT", "checkFieldOID": "DM.BRTHDAT", "checkItemName": "年龄", "operator": ">=", "numericValue": 18, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄必须≥18岁"}, {"id": 2, "tableName": "DM", "checkItemOID": "DM.BRTHDAT", "checkFieldOID": "DM.BRTHDAT", "checkItemName": "年龄", "operator": "<=", "numericValue": 70, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄必须≤70岁"}, {"id": 3, "tableName": "RSECOG", "checkItemOID": "RSECOG.RSORRES", "checkFieldOID": "RSECOG.RSORRES", "checkItemName": "ECOG评分", "operator": "<=", "numericValue": 1, "nonNumericValue": null, "gender": null, "unit": "分", "message": "ECOG评分必须≤1分"}, {"id": 4, "tableName": "LB_SUB", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LB_SUB.LBORRES", "checkItemName": "ANC", "operator": ">=", "numericValue": 1.0, "nonNumericValue": null, "gender": null, "unit": "10^9/L", "message": "ANC必须≥1.0×10^9/L"}, {"id": 5, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EG_SUB.EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 450, "nonNumericValue": null, "gender": "Male", "unit": "ms", "message": "QTc间期超过450ms(男性)"}, {"id": 6, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EG_SUB.EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 470, "nonNumericValue": null, "gender": "Female", "unit": "ms", "message": "QTc间期超过470ms(女性)"}]