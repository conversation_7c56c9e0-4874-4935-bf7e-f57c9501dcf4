"""
临床试验标准化信息抽取系统 - 命令行版本
基于优化后的LLM驱动抽取器
"""

import argparse
import json
from optimized_llm_extractor import OptimizedLLMExtractor


def demo_mode():
    """演示模式"""
    
    print("\n🎯 演示模式（优化LLM智能抽取）")
    print("=" * 60)
    
    # 使用优化后的LLM驱动抽取器
    extractor = OptimizedLLMExtractor(max_iterations=3)
    
    # 演示案例1：标准临床试验
    print("\n📋 演示案例1: 标准临床试验")
    standard_text = """入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
ANC≥1.0×10^9/L；
血小板计数≥75×10^9/L；
排除标准:
QTc间期延长（男性>450ms、女性>470ms）；
HBV DNA≥2×10³ IU/mL；"""
    
    standard_ecrf = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值
病毒学定量检查明细\tLBVT_SUB\t检查项\tLBTEST\t文本
病毒学定量检查明细\tLBVT_SUB\t检测值\tLBORRES\t数值"""
    
    rules1 = extractor.extract_rules_only(standard_text, standard_ecrf)
    print(f"✅ 提取了 {len(rules1)} 条标准规则")
    
    # 演示案例2：未知检查类型
    print("\n📋 演示案例2: 未知检查类型")
    novel_text = """入选标准:
新型生物标志物ABC≥100 ng/mL；
特殊免疫指标XYZ阴性；
实验性评分DEF≤5分；
排除标准:
罕见遗传标记GHI阳性；
未知病毒JKL RNA≥1000 copies/mL；"""
    
    novel_ecrf = """生物标志物检查\tBIOMAR\t标志物名称\tBMTEST\t文本
生物标志物检查\tBIOMAR\t检测结果\tBMORRES\t数值
免疫学检查\tIMMUNO\t检查项目\tIMTEST\t文本
免疫学检查\tIMMUNO\t检测结果\tIMORRES\t单选
特殊评分\tSPECIAL\t评分项目\tSPTEST\t文本
特殊评分\tSPECIAL\t评分结果\tSPORRES\t数值
遗传学检查\tGENET\t基因标记\tGNTEST\t文本
遗传学检查\tGENET\t检测结果\tGNORRES\t单选"""
    
    rules2 = extractor.extract_rules_only(novel_text, novel_ecrf)
    print(f"✅ 提取了 {len(rules2)} 条新型规则")
    
    # 保存演示结果
    all_demo_rules = rules1 + rules2
    with open("demo_results.json", "w", encoding="utf-8") as f:
        json.dump(all_demo_rules, f, ensure_ascii=False, indent=2)
    print("✅ 结果已保存到: demo_results.json")
    
    print(f"\n🎉 演示完成！共展示了系统处理 {len(all_demo_rules)} 条规则的能力")


def interactive_mode():
    """交互模式"""
    
    print("\n🎯 交互模式（优化LLM智能抽取）")
    print("您可以输入临床文本和eCRF信息进行实时抽取")
    print("输入 'quit' 退出")
    print("=" * 60)
    
    # 使用优化后的LLM驱动抽取器
    extractor = OptimizedLLMExtractor(max_iterations=3)
    
    while True:
        print("\n请输入临床文本:")
        clinical_text = input().strip()
        
        if clinical_text.lower() == 'quit':
            break
        
        print("\n请输入eCRF信息 (可选，按回车使用默认):")
        ecrf_input = input().strip()
        
        if not ecrf_input:
            # 使用扩展的默认eCRF信息
            ecrf_info = """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选
生命体征明细\tVS_SUB\t检查项\tVSTEST\t文本
生命体征明细\tVS_SUB\t结果\tVSORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选
实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值
病史明细\tMH\t病史描述\tMHTERM\t文本
病史明细\tMH\t是否存在\tMHOCCUR\t单选
心理评估\tPSYCH\t评估项目\tPSTEST\t文本
心理评估\tPSYCH\t评估结果\tPSORRES\t单选
生活习惯\tLIFESTYLE\t习惯类型\tLSTYPE\t文本
生活习惯\tLIFESTYLE\t频率数值\tLSFREQ\t数值"""
        else:
            ecrf_info = ecrf_input
        
        # 执行优化LLM智能抽取
        print("\n🧠 正在进行优化LLM智能分析和抽取...")
        result = extractor.extract_with_full_analysis(clinical_text, ecrf_info)
        rules = result.get("rules", [])
        
        # 显示LLM分析过程
        print(f"\n📋 临床文本分析:")
        print(result.get("clinical_analysis", "无")[:500] + "..." if len(result.get("clinical_analysis", "")) > 500 else result.get("clinical_analysis", "无"))
        
        print(f"\n⚡ 推理过程:")
        print(result.get("reasoning_process", "无")[:500] + "..." if len(result.get("reasoning_process", "")) > 500 else result.get("reasoning_process", "无"))
        
        print(f"\n🔄 迭代优化: 使用了 {result.get('iterations_used', 0)} 次迭代")
        
        # 显示抽取结果
        print(f"\n✅ 成功提取了 {len(rules)} 条规则:")
        for i, rule in enumerate(rules[:8], 1):  # 显示前8条
            print(f"\n规则 {i}:")
            print(f"  检查项: {rule.get('checkItemName', 'N/A')}")
            print(f"  表名: {rule.get('tableName', 'N/A')}")
            print(f"  字段: {rule.get('checkFieldOID', 'N/A')}")
            print(f"  操作符: {rule.get('operator', 'N/A')}")
            print(f"  数值: {rule.get('numericValue', rule.get('nonNumericValue', 'N/A'))}")
            print(f"  性别: {rule.get('gender', 'N/A')}")
            print(f"  临床依据: {rule.get('clinicalRationale', 'N/A')}")
        
        if len(rules) > 8:
            print(f"\n... 还有 {len(rules) - 8} 条规则未显示")
        
        # 询问是否保存
        save_choice = input("\n是否保存结果? (y/n): ").lower()
        if save_choice == 'y':
            filename = input("请输入文件名 (默认: interactive_results.json): ").strip()
            if not filename:
                filename = "interactive_results.json"
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {filename}")


def extract_mode(input_file: str, ecrf_file: str, output_file: str):
    """文件抽取模式"""
    
    print(f"\n🎯 文件抽取模式")
    print("=" * 60)
    
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            clinical_text = f.read()
        with open(ecrf_file, 'r', encoding='utf-8') as f:
            ecrf_info = f.read()
        
        print(f"📄 从文件抽取: {input_file}")
        
        # 使用优化后的LLM驱动抽取器
        extractor = OptimizedLLMExtractor(max_iterations=3)
        
        # 执行优化LLM智能抽取
        result = extractor.extract_with_full_analysis(clinical_text, ecrf_info)
        rules = result.get("rules", [])
        
        if rules:
            print(f"✅ 成功提取了 {len(rules)} 条规则")
            print(f"🔄 使用了 {result.get('iterations_used', 0)} 次迭代优化")
            
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {output_file}")
        else:
            print("❌ 抽取失败或未找到规则")
            
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
    except Exception as e:
        print(f"❌ 抽取过程出错: {e}")


def main():
    """主函数"""
    
    print("🎯 临床试验标准化信息抽取系统")
    print("基于优化LLM驱动的智能抽取器")
    print("=" * 80)
    
    parser = argparse.ArgumentParser(description="临床信息抽取系统")
    parser.add_argument("--mode", choices=["demo", "interactive", "extract"],
                       default="demo", help="运行模式")
    parser.add_argument("--input", type=str, help="输入文件路径")
    parser.add_argument("--ecrf", type=str, help="eCRF文件路径")
    parser.add_argument("--output", type=str, default="results.json", help="输出文件路径")
    
    args = parser.parse_args()
    
    if args.mode == "demo":
        demo_mode()
    
    elif args.mode == "interactive":
        interactive_mode()
    

    
    elif args.mode == "extract":
        if not args.input or not args.ecrf:
            print("❌ 抽取模式需要提供 --input 和 --ecrf 参数")
            return
        
        extract_mode(args.input, args.ecrf, args.output)
    
    print("\n" + "=" * 80)
    print("🎉 程序执行完成！")
    print("=" * 80)


if __name__ == "__main__":
    main()
