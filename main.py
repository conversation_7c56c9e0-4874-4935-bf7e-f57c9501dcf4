# -*- coding: utf-8 -*-
"""
临床试验标准化信息抽取系统 - 模块化高并发API
基于优化后的LLM驱动抽取器，支持SQLite持久化和高并发处理
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.routes import router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动事件
    print("🚀 临床试验信息抽取API启动中...")
    print("📊 数据库初始化完成")
    print("🎯 服务已就绪，等待请求...")

    yield

    # 关闭事件
    print("🛑 临床试验信息抽取API正在关闭...")

    # 清理资源
    from services.log_manager import log_manager
    log_manager.cleanup_all_streams()

    print("✅ 资源清理完成")


# 创建FastAPI应用
app = FastAPI(
    title="临床试验信息抽取API - 模块化版",
    description="基于LLM的智能临床试验标准化信息抽取服务，支持SQLite持久化和高并发异步处理",
    version="3.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(router)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "临床试验标准化信息抽取API - 模块化版",
        "version": "3.0.0",
        "status": "running",
        "features": [
            "SQLite持久化存储",
            "实时日志流",
            "高并发异步处理",
            "批量任务支持",
            "模块化架构"
        ]
    }





if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
