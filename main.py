"""
临床试验标准化信息抽取系统 - 高并发异步API
基于优化后的LLM驱动抽取器，支持高并发处理
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import json
import uuid
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading
from optimized_llm_extractor import OptimizedLLMExtractor

# 创建FastAPI应用
app = FastAPI(
    title="临床试验信息抽取API - 高并发版",
    description="基于LLM的智能临床试验标准化信息抽取服务，支持高并发异步处理",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 线程池执行器（用于CPU密集型任务）
executor = ThreadPoolExecutor(max_workers=8)  # 增加并发数

# 任务存储（生产环境应使用Redis）
tasks = {}
tasks_lock = threading.Lock()

# 抽取器池（避免重复创建）
extractor_pool = []
pool_lock = threading.Lock()

def get_extractor(max_iterations: int = 3) -> OptimizedLLMExtractor:
    """从池中获取抽取器实例"""
    with pool_lock:
        if extractor_pool:
            extractor = extractor_pool.pop()
            extractor.max_iterations = max_iterations
            return extractor
        else:
            return OptimizedLLMExtractor(max_iterations=max_iterations)

def return_extractor(extractor: OptimizedLLMExtractor):
    """归还抽取器到池中"""
    with pool_lock:
        if len(extractor_pool) < 10:  # 限制池大小
            extractor_pool.append(extractor)


class ExtractionRequest(BaseModel):
    """抽取请求模型"""
    clinical_text: str = Field(..., description="临床试验的入组和排除标准文本")
    ecrf_info: str = Field(..., description="eCRF表结构信息")
    max_iterations: Optional[int] = Field(3, description="最大迭代次数", ge=1, le=5)


class ExtractionRule(BaseModel):
    """抽取规则模型"""
    id: int
    tableName: str
    checkItemOID: str
    checkFieldOID: str
    checkItemName: str
    operator: str
    numericValue: Optional[Any] = None
    nonNumericValue: Optional[str] = None
    gender: Optional[str] = None
    unit: Optional[str] = None
    message: str
    clinicalRationale: str


class ExtractionResponse(BaseModel):
    """抽取响应模型"""
    task_id: str
    status: str
    clinical_analysis: Optional[str] = None
    ecrf_analysis: Optional[str] = None
    reasoning_process: Optional[str] = None
    validation_history: Optional[List[str]] = None
    optimization_history: Optional[List[str]] = None
    iterations_used: Optional[int] = None
    rules: List[ExtractionRule] = []
    total_rules: int = 0
    created_at: str
    completed_at: Optional[str] = None
    error: Optional[str] = None


class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str
    progress: str
    created_at: str
    completed_at: Optional[str] = None


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "临床试验标准化信息抽取API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.post("/extract", response_model=dict)
async def extract_clinical_info(request: ExtractionRequest):
    """
    提交临床信息抽取任务（完全异步）

    - **clinical_text**: 临床试验的入组和排除标准文本
    - **ecrf_info**: eCRF表结构信息
    - **max_iterations**: 最大迭代次数（1-5）
    """

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 初始化任务状态
    with tasks_lock:
        tasks[task_id] = ExtractionResponse(
            task_id=task_id,
            status="pending",
            total_rules=0,
            created_at=datetime.now().isoformat()
        )

    # 异步启动抽取任务
    asyncio.create_task(process_extraction_async(
        task_id,
        request.clinical_text,
        request.ecrf_info,
        request.max_iterations
    ))

    return {
        "task_id": task_id,
        "status": "pending",
        "message": "抽取任务已提交，请使用task_id查询结果"
    }


@app.get("/extract/{task_id}", response_model=ExtractionResponse)
async def get_extraction_result(task_id: str):
    """
    获取抽取任务结果
    
    - **task_id**: 任务ID
    """
    
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return tasks[task_id]


@app.get("/tasks", response_model=List[TaskStatus])
async def list_tasks():
    """获取所有任务状态"""
    
    return [
        TaskStatus(
            task_id=task_id,
            status=task.status,
            progress=f"已提取{task.total_rules}条规则" if task.status == "completed" else task.status,
            created_at=task.created_at,
            completed_at=task.completed_at
        )
        for task_id, task in tasks.items()
    ]


@app.delete("/tasks/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""

    with tasks_lock:
        if task_id not in tasks:
            raise HTTPException(status_code=404, detail="任务不存在")

        del tasks[task_id]

    return {"message": "任务已删除"}


@app.post("/extract/batch", response_model=dict)
async def extract_batch(requests: List[ExtractionRequest]):
    """
    批量提交抽取任务（高并发）

    - **requests**: 抽取请求列表
    """

    if len(requests) > 50:  # 限制批量大小
        raise HTTPException(status_code=400, detail="批量任务数量不能超过50个")

    task_ids = []

    for request in requests:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        task_ids.append(task_id)

        # 初始化任务状态
        with tasks_lock:
            tasks[task_id] = ExtractionResponse(
                task_id=task_id,
                status="pending",
                total_rules=0,
                created_at=datetime.now().isoformat()
            )

        # 异步启动抽取任务
        asyncio.create_task(process_extraction_async(
            task_id,
            request.clinical_text,
            request.ecrf_info,
            request.max_iterations
        ))

    return {
        "task_ids": task_ids,
        "total_tasks": len(task_ids),
        "status": "pending",
        "message": f"已提交{len(task_ids)}个批量抽取任务"
    }


async def process_extraction_async(
    task_id: str,
    clinical_text: str,
    ecrf_info: str,
    max_iterations: int
):
    """异步处理抽取任务"""

    try:
        # 更新任务状态
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id].status = "processing"

        # 在线程池中执行CPU密集型任务
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            run_extraction,
            clinical_text,
            ecrf_info,
            max_iterations
        )

        # 转换规则格式
        rules = [
            ExtractionRule(**rule) for rule in result.get("rules", [])
        ]

        # 更新任务结果
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id] = ExtractionResponse(
                    task_id=task_id,
                    status="completed",
                    clinical_analysis=result.get("clinical_analysis"),
                    ecrf_analysis=result.get("ecrf_analysis"),
                    reasoning_process=result.get("reasoning_process"),
                    validation_history=result.get("validation_history", []),
                    optimization_history=result.get("optimization_history", []),
                    iterations_used=result.get("iterations_used", 0),
                    rules=rules,
                    total_rules=len(rules),
                    created_at=tasks[task_id].created_at,
                    completed_at=datetime.now().isoformat()
                )

    except Exception as e:
        # 更新错误状态
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id].status = "failed"
                tasks[task_id].error = str(e)
                tasks[task_id].completed_at = datetime.now().isoformat()


def run_extraction(clinical_text: str, ecrf_info: str, max_iterations: int) -> Dict[str, Any]:
    """在线程池中运行的抽取函数（使用对象池）"""
    extractor = get_extractor(max_iterations)
    try:
        result = extractor.extract_with_full_analysis(clinical_text, ecrf_info)
        return result
    finally:
        return_extractor(extractor)





if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
