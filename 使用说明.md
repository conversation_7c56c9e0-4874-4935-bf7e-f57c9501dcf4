# 临床试验标准化信息抽取系统 - 使用说明

## 🎯 系统简介

基于DSPy框架的临床试验标准化信息抽取系统，专门处理复杂的真实临床试验案例，能够从临床试验的入组和排除标准文本中自动提取结构化的检查规则。

**🚀 生产模式**: 系统默认使用高级抽取器，提供详细的分析步骤和完整的规则抽取。

## 📁 核心文件

```
clinical_extractor.py    # 核心抽取模块 ⭐
advanced_extractor.py    # 高级抽取器（处理复杂案例）⭐
optimizer.py             # DSPy优化器 ⭐
training_manager.py      # 训练数据管理器 ⭐
app.py                   # 主程序入口 ⭐
llm_config.json         # LLM配置文件（自动生成）
training_examples.json  # 训练示例数据（自动生成）
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install dspy
```

### 2. 配置LLM

首次使用需要配置LLM：

```bash
python app.py --mode config
```

按提示输入：
- 模型名称：`openai/DeepSeek-V3`
- API密钥：您的API密钥
- API地址：`https://llmaas.hengrui.com/v1`
- 缓存：`n`

### 3. 运行演示

```bash
python app.py --mode demo
```

## 📖 使用方式

### 演示模式
```bash
python app.py --mode demo
```
运行预设的演示案例，展示系统能力。使用高级抽取器处理复杂案例。

### 交互模式
```bash
python app.py --mode interactive
```
进入交互式界面，可以实时输入文本进行抽取。提供详细的分析步骤和完整的规则抽取。

### 文件抽取模式
```bash
python app.py --mode extract --input clinical.txt --ecrf ecrf.txt --output results.json
```

### 配置模式
```bash
python app.py --mode config
```
更新LLM配置信息。

### 优化模式
```bash
python app.py --mode optimize
```
使用DSPy的BootstrapFewShot优化器提升模型性能。

### 训练数据管理模式
```bash
python app.py --mode training
```
管理训练示例，支持添加、删除、查看训练数据。



## 📝 输入格式

### 临床文本示例
```
入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
ANC≥1.0×10^9/L；
血小板计数≥75×10^9/L；

排除标准:
QTc间期延长（男性>450ms、女性>470ms）；
HBV DNA≥2×10³ IU/mL；
```

### eCRF信息示例
```
人口学资料	DM	出生日期	BRTHDAT	日期时间
ECOG评分	RSECOG	ECOG评分分值	RSORRES	单选
实验室检查明细	LB_SUB	检查项	LBTEST	文本
实验室检查明细	LB_SUB	结果	LBORRES	数值
12导联心电图明细	EG_SUB	检查项	EGTEST	文本
12导联心电图明细	EG_SUB	结果	EGORRES	数值
```

## 📊 输出格式

系统输出标准化的JSON格式：

```json
[
  {
    "id": 1,
    "tableName": "LB_SUB",
    "checkItemOID": "LB_SUB.LBTEST",
    "checkFieldOID": "LBORRES",
    "checkItemName": "ANC",
    "operator": "<",
    "numericValue": 1.0,
    "nonNumericValue": null,
    "gender": null,
    "unit": "10^9/L",
    "message": "ANC<1.0×10^9/L，不满足入选标准，请核实"
  }
]
```

## 🔧 高级功能

### 1. 灵活的表映射
系统能够自动识别未知的检查类型，无需预定义映射表。

### 2. 智能字段匹配
基于eCRF结构自动选择最合适的表名和字段名。

### 3. 性别特异性规则
自动识别和处理性别特异性的检查标准（如QTc间期）。

### 4. 多种检查类型支持
- 年龄限制
- 实验室指标
- 心电图检查
- 病毒学检查
- 功能评分
- 生命体征

## 🛠️ 更换LLM模型

### 方法1：使用配置模式
```bash
python app.py --mode config
```

### 方法2：直接编辑配置文件
编辑 `llm_config.json`：
```json
{
  "model_name": "your-model-name",
  "api_key": "your-api-key",
  "api_base": "your-api-base",
  "cache": false
}
```

### 方法3：代码中修改
```python
from clinical_extractor import ClinicalExtractor, LLMConfig

config = LLMConfig(
    model_name="your-model-name",
    api_key="your-api-key",
    api_base="your-api-base",
    cache=False
)

extractor = ClinicalExtractor(config)
```

## 📋 支持的LLM模型

理论上支持所有兼容OpenAI API格式的模型：
- OpenAI GPT系列
- DeepSeek系列
- Claude系列（通过代理）
- 本地部署的开源模型

## ❓ 常见问题

### Q: 如何处理新的检查类型？
A: 系统具有自动识别能力，只需在eCRF信息中提供相应的表结构即可。

### Q: 抽取结果不准确怎么办？
A: 检查输入的临床文本格式和eCRF信息是否正确，确保表结构清晰。

### Q: 如何批量处理多个文件？
A: 可以编写简单的脚本调用 `ClinicalExtractor.extract_from_file()` 方法。

### Q: 系统支持哪些语言？
A: 目前主要支持中文临床文本，英文支持有限。

## 📞 技术支持

如有问题，请检查：
1. LLM配置是否正确
2. 输入格式是否符合要求
3. 网络连接是否正常
4. API密钥是否有效

## 🎉 开始使用

```bash
# 1. 配置LLM
python app.py --mode config

# 2. 运行演示
python app.py --mode demo

# 3. 开始使用
python app.py --mode interactive
```

祝您使用愉快！
