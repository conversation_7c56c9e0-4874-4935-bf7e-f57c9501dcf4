"""
终极LLM驱动的临床信息抽取器
确保迭代优化直到完全通过验证
"""

import dspy
import json
from typing import List, Dict, Any
from llm_powered_extractor import LLMPoweredExtractor


# 配置LLM模型
lm = dspy.LM("openai/DeepSeek-V3", 
             api_key="sP8TpUbNGBZfEYI", 
             api_base="https://llmaas.hengrui.com/v1",
             cache=False)
dspy.configure(lm=lm)


class UltimateRuleGenerator(dspy.Signature):
    """终极规则生成器
    
    专门处理复杂案例，确保完整覆盖所有检查条件
    """
    
    clinical_text: str = dspy.InputField(desc="完整的临床试验文本")
    ecrf_structure: str = dspy.InputField(desc="eCRF结构信息")
    previous_attempt: str = dspy.InputField(desc="之前的抽取尝试和问题")
    
    comprehensive_analysis: str = dspy.OutputField(
        desc="""进行最全面的分析，确保不遗漏任何检查条件：
        
        1. 逐条分析每个入组标准和排除标准
        2. 识别所有可量化的检查项目
        3. 分析隐含的检查要求
        4. 考虑临床实践中的必要检查
        5. 确保覆盖所有医学领域（人口学、实验室、病史、生活方式等）
        
        请提供详尽的分析，确保完整性。"""
    )
    
    complete_rules: str = dspy.OutputField(
        desc="""生成完整的检查规则JSON数组，必须：
        
        1. 覆盖原文中的每一个检查条件
        2. 包含所有入组标准的检查规则
        3. 包含所有排除标准的检查规则
        4. 处理复合条件和隐含条件
        5. 提供准确的表映射和字段选择
        6. 每个规则都有清晰的临床依据
        
        确保规则数量足够覆盖复杂案例（通常应该有20-30条规则）。
        输出格式必须是有效的JSON数组。"""
    )


class StrictValidator(dspy.Signature):
    """严格验证器
    
    对规则进行最严格的验证，确保完全合格
    """
    
    original_text: str = dspy.InputField(desc="原始临床文本")
    extracted_rules: str = dspy.InputField(desc="提取的规则JSON")
    
    detailed_validation: str = dspy.OutputField(
        desc="""进行最严格的验证，逐项检查：
        
        1. 完整性：是否覆盖了原文中的每一个检查条件？
        2. 准确性：每个规则是否准确反映原文意图？
        3. 逻辑性：规则之间是否存在冲突或重复？
        4. 临床性：规则是否符合临床实践标准？
        5. 技术性：表映射和字段选择是否正确？
        
        请逐条列出所有发现的问题，不要遗漏任何细节。"""
    )
    
    pass_or_fail: str = dspy.OutputField(
        desc="""最终判定结果，只能是以下两种之一：
        
        - "通过" - 如果规则完全满足所有要求
        - "不通过" - 如果仍有任何问题需要解决
        
        判定标准极其严格，只有真正完美的结果才能通过。"""
    )


class UltimateExtractor:
    """终极抽取器，确保迭代优化直到完全通过"""
    
    def __init__(self, max_iterations: int = 5):
        self.ultimate_generator = dspy.ChainOfThought(UltimateRuleGenerator)
        self.strict_validator = dspy.ChainOfThought(StrictValidator)
        self.max_iterations = max_iterations
    
    def extract_until_perfect(self, clinical_text: str, ecrf_info: str) -> Dict[str, Any]:
        """迭代抽取直到完美通过验证"""
        
        print("🎯 启动终极抽取器")
        print("=" * 60)
        
        previous_attempt = "首次尝试"
        validation_history = []
        generation_history = []
        
        for iteration in range(self.max_iterations):
            print(f"\n🔄 第 {iteration + 1}/{self.max_iterations} 次迭代")
            
            # 生成规则
            print("   📝 生成规则...")
            generation = self.ultimate_generator(
                clinical_text=clinical_text,
                ecrf_structure=ecrf_info,
                previous_attempt=previous_attempt
            )
            generation_history.append(generation.comprehensive_analysis)
            
            # 严格验证
            print("   🔍 严格验证...")
            validation = self.strict_validator(
                original_text=clinical_text,
                extracted_rules=generation.complete_rules
            )
            validation_history.append(validation.detailed_validation)
            
            # 检查是否通过
            if validation.pass_or_fail.strip() == "通过":
                print(f"   ✅ 第 {iteration + 1} 次迭代通过验证！")
                
                # 解析最终规则
                try:
                    final_rules = json.loads(generation.complete_rules)
                    if not isinstance(final_rules, list):
                        final_rules = [final_rules] if final_rules else []
                except json.JSONDecodeError as e:
                    print(f"   ⚠️ JSON解析失败: {e}")
                    final_rules = []
                
                return {
                    "success": True,
                    "iterations_used": iteration + 1,
                    "final_analysis": generation.comprehensive_analysis,
                    "generation_history": generation_history,
                    "validation_history": validation_history,
                    "final_validation": validation.detailed_validation,
                    "rules": final_rules,
                    "total_rules": len(final_rules)
                }
            else:
                print(f"   ❌ 第 {iteration + 1} 次迭代未通过验证")
                print(f"   📋 问题: {validation.detailed_validation[:200]}...")
                
                # 准备下次迭代
                previous_attempt = f"""
                第{iteration + 1}次尝试结果:
                生成的规则: {generation.complete_rules[:1000]}...
                验证问题: {validation.detailed_validation}
                
                请基于这些问题进行改进。
                """
        
        # 如果达到最大迭代次数仍未通过
        print(f"\n⚠️ 达到最大迭代次数 ({self.max_iterations})，使用最佳结果")
        
        try:
            final_rules = json.loads(generation.complete_rules)
            if not isinstance(final_rules, list):
                final_rules = [final_rules] if final_rules else []
        except:
            final_rules = []
        
        return {
            "success": False,
            "iterations_used": self.max_iterations,
            "final_analysis": generation.comprehensive_analysis,
            "generation_history": generation_history,
            "validation_history": validation_history,
            "final_validation": validation.detailed_validation,
            "rules": final_rules,
            "total_rules": len(final_rules)
        }


def test_ultimate_extractor():
    """测试终极抽取器"""
    
    print("🎯 测试终极LLM抽取器")
    print("=" * 60)
    
    # 使用复杂的真实案例
    with open("./demo/test.txt", "r", encoding="utf-8") as f:
        clinical_text = f.read()
    
    with open("./demo/eCRF.txt", "r", encoding="utf-8") as f:
        ecrf_info = f.read()
    
    # 创建终极抽取器
    extractor = UltimateExtractor(max_iterations=5)
    
    # 执行抽取
    result = extractor.extract_until_perfect(clinical_text, ecrf_info)
    
    # 显示结果
    print(f"\n🎯 最终结果:")
    print(f"   成功: {'是' if result['success'] else '否'}")
    print(f"   迭代次数: {result['iterations_used']}")
    print(f"   规则数量: {result['total_rules']}")
    
    print(f"\n📋 最终分析:")
    print(result['final_analysis'][:500] + "..." if len(result['final_analysis']) > 500 else result['final_analysis'])
    
    print(f"\n✅ 最终验证:")
    print(result['final_validation'][:500] + "..." if len(result['final_validation']) > 500 else result['final_validation'])
    
    # 显示部分规则
    print(f"\n📝 规则示例 (前5条):")
    for i, rule in enumerate(result['rules'][:5], 1):
        print(f"   规则 {i}: {rule.get('checkItemName', 'N/A')} - {rule.get('tableName', 'N/A')}")
    
    # 保存结果
    with open("ultimate_results.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: ultimate_results.json")
    
    return result


if __name__ == "__main__":
    test_ultimate_extractor()
