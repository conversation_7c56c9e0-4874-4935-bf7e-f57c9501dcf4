# 临床试验标准化信息抽取API - 高并发版

基于优化后的LLM驱动抽取器的高并发FastAPI服务，支持完全异步处理和批量抽取。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8000` 启动

### 3. 查看API文档

访问 `http://localhost:8000/docs` 查看交互式API文档

## 📖 API接口

### 1. 健康检查

```http
GET /health
```

### 2. 异步抽取（完全异步）

```http
POST /extract
```

**请求体：**
```json
{
  "clinical_text": "入选标准:\n年龄18~55周岁，男女均可；\n男性体重≥50kg，女性体重≥45kg；",
  "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间\n人口学资料\tDM\t性别\tSEX\t单选",
  "max_iterations": 3
}
```

**响应：**
```json
{
  "task_id": "uuid-string",
  "status": "pending",
  "message": "抽取任务已提交，请使用task_id查询结果"
}
```

### 3. 查询任务结果

```http
GET /extract/{task_id}
```

**响应：**
```json
{
  "task_id": "uuid-string",
  "status": "completed",
  "clinical_analysis": "详细的临床文本分析...",
  "ecrf_analysis": "详细的eCRF结构分析...",
  "reasoning_process": "详细的推理过程...",
  "validation_history": ["验证历史..."],
  "optimization_history": ["优化历史..."],
  "iterations_used": 2,
  "rules": [
    {
      "id": 1,
      "tableName": "DM",
      "checkItemOID": "DM.AGE",
      "checkFieldOID": "DM.BRTHDAT",
      "checkItemName": "年龄",
      "operator": "BETWEEN",
      "numericValue": [18, 55],
      "nonNumericValue": null,
      "gender": null,
      "unit": "岁",
      "message": "受试者年龄必须在18-55岁之间",
      "clinicalRationale": "确保受试者生理状态相近"
    }
  ],
  "total_rules": 10,
  "created_at": "2024-01-01T10:00:00",
  "completed_at": "2024-01-01T10:05:00"
}
```

### 4. 批量抽取（高并发）

```http
POST /extract/batch
```

**请求体：**
```json
[
  {
    "clinical_text": "入选标准1...",
    "ecrf_info": "eCRF信息1...",
    "max_iterations": 3
  },
  {
    "clinical_text": "入选标准2...",
    "ecrf_info": "eCRF信息2...",
    "max_iterations": 3
  }
]
```

**响应：**
```json
{
  "task_ids": ["uuid1", "uuid2"],
  "total_tasks": 2,
  "status": "pending",
  "message": "已提交2个批量抽取任务"
}
```

### 5. 任务管理

```http
GET /tasks          # 获取所有任务
DELETE /tasks/{id}  # 删除任务
```

## 💻 使用示例

### Python客户端

```python
import requests
import time

# 1. 提交抽取任务
response = requests.post("http://localhost:8000/extract", json={
    "clinical_text": """入选标准:
年龄18~55周岁，男女均可；
男性体重≥50kg，女性体重≥45kg，BMI 19~28 kg/m²；
有生育能力的女性妊娠试验阴性；

排除标准:
既往有心律失常病史；
乙肝表面抗原、HIV抗体阳性；""",
    "ecrf_info": """人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选
生命体征明细\tVS_SUB\t检查项\tVSTEST\t文本
生命体征明细\tVS_SUB\t结果\tVSORRES\t数值
实验室定性检查明细\tLBQUAL_SUB\t检查项\tLBTEST\t文本
实验室定性检查明细\tLBQUAL_SUB\t结果\tLBORRES\t单选""",
    "max_iterations": 3
})

task_id = response.json()["task_id"]
print(f"任务ID: {task_id}")

# 2. 轮询任务状态
while True:
    result = requests.get(f"http://localhost:8000/extract/{task_id}")
    data = result.json()
    
    if data["status"] == "completed":
        print(f"抽取完成！共提取了 {data['total_rules']} 条规则")
        print(f"迭代次数: {data['iterations_used']}")
        
        # 显示规则
        for rule in data["rules"][:3]:
            print(f"- {rule['checkItemName']}: {rule['message']}")
        break
    elif data["status"] == "failed":
        print(f"抽取失败: {data['error']}")
        break
    else:
        print(f"状态: {data['status']}")
        time.sleep(5)
```

### JavaScript客户端

```javascript
// 1. 提交抽取任务
const response = await fetch('http://localhost:8000/extract', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        clinical_text: `入选标准:
年龄18~55周岁，男女均可；
男性体重≥50kg，女性体重≥45kg；`,
        ecrf_info: `人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
人口学资料\tDM\t性别\tSEX\t单选`,
        max_iterations: 3
    })
});

const { task_id } = await response.json();
console.log('任务ID:', task_id);

// 2. 轮询任务状态
const pollResult = async () => {
    const result = await fetch(`http://localhost:8000/extract/${task_id}`);
    const data = await result.json();
    
    if (data.status === 'completed') {
        console.log(`抽取完成！共提取了 ${data.total_rules} 条规则`);
        console.log('规则:', data.rules);
    } else if (data.status === 'failed') {
        console.log('抽取失败:', data.error);
    } else {
        console.log('状态:', data.status);
        setTimeout(pollResult, 5000);
    }
};

pollResult();
```

### cURL示例

```bash
# 1. 提交任务
curl -X POST "http://localhost:8000/extract" \
     -H "Content-Type: application/json" \
     -d '{
       "clinical_text": "入选标准:\n年龄18~55周岁；",
       "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间",
       "max_iterations": 3
     }'

# 2. 查询结果
curl "http://localhost:8000/extract/{task_id}"

# 3. 同步抽取
curl -X POST "http://localhost:8000/extract/sync" \
     -H "Content-Type: application/json" \
     -d '{
       "clinical_text": "入选标准:\n年龄18~55周岁；",
       "ecrf_info": "人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间"
     }'
```

## 🔧 配置说明

### 环境变量

可以通过环境变量配置LLM参数：

```bash
export LLM_MODEL="openai/DeepSeek-V3"
export LLM_API_KEY="your-api-key"
export LLM_API_BASE="https://llmaas.hengrui.com/v1"
```

### 性能调优

- `max_iterations`: 控制迭代优化次数（1-5）
- 复杂案例建议使用异步接口
- 简单案例可使用同步接口

## 📊 性能指标

- **简单案例**: 通常1-2次迭代，30-60秒完成
- **复杂案例**: 通常2-3次迭代，2-5分钟完成
- **抽取效果**: 复杂案例可提取30+条规则

## 🚀 部署

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "main.py"]
```

### 生产环境

建议使用：
- Redis存储任务状态
- PostgreSQL存储历史记录
- Nginx反向代理
- 多进程部署

## 📞 技术支持

如有问题，请检查：
1. LLM配置是否正确
2. 输入格式是否符合要求
3. 网络连接是否正常
4. API密钥是否有效
