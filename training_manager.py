"""
训练数据管理器
支持动态添加新的训练场景和示例
"""

import json
import os
from typing import List, Dict, Any
import dspy
from datetime import datetime


class TrainingExample:
    """训练示例类"""
    
    def __init__(self, name: str, clinical_text: str, ecrf_info: str, expected_rules: List[Dict[str, Any]], 
                 category: str = "general", description: str = ""):
        self.name = name
        self.clinical_text = clinical_text
        self.ecrf_info = ecrf_info
        self.expected_rules = expected_rules
        self.category = category
        self.description = description
        self.created_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "clinical_text": self.clinical_text,
            "ecrf_info": self.ecrf_info,
            "expected_rules": self.expected_rules,
            "category": self.category,
            "description": self.description,
            "created_at": self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrainingExample':
        """从字典创建实例"""
        return cls(
            name=data["name"],
            clinical_text=data["clinical_text"],
            ecrf_info=data["ecrf_info"],
            expected_rules=data["expected_rules"],
            category=data.get("category", "general"),
            description=data.get("description", "")
        )
    
    def to_dspy_example(self) -> dspy.Example:
        """转换为DSPy Example格式"""
        return dspy.Example(
            clinical_text=self.clinical_text,
            ecrf_info=self.ecrf_info,
            expected_rules=self.expected_rules
        ).with_inputs("clinical_text", "ecrf_info")


class TrainingManager:
    """训练数据管理器"""
    
    def __init__(self, data_file: str = "training_examples.json"):
        self.data_file = data_file
        self.examples = []
        self.load_examples()
    
    def load_examples(self):
        """加载训练示例"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.examples = [TrainingExample.from_dict(item) for item in data]
                print(f"✅ 已加载 {len(self.examples)} 个训练示例")
            except Exception as e:
                print(f"⚠️ 加载训练示例失败: {e}")
                self.examples = []
                self._create_default_examples()
        else:
            print("📝 创建默认训练示例")
            self._create_default_examples()
    
    def save_examples(self):
        """保存训练示例"""
        try:
            data = [example.to_dict() for example in self.examples]
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 已保存 {len(self.examples)} 个训练示例到 {self.data_file}")
        except Exception as e:
            print(f"❌ 保存训练示例失败: {e}")
    
    def add_example(self, example: TrainingExample):
        """添加新的训练示例"""
        # 检查是否已存在同名示例
        existing_names = [ex.name for ex in self.examples]
        if example.name in existing_names:
            print(f"⚠️ 示例 '{example.name}' 已存在，将覆盖")
            self.examples = [ex for ex in self.examples if ex.name != example.name]
        
        self.examples.append(example)
        self.save_examples()
        print(f"✅ 已添加训练示例: {example.name}")
    
    def remove_example(self, name: str):
        """删除训练示例"""
        original_count = len(self.examples)
        self.examples = [ex for ex in self.examples if ex.name != name]
        
        if len(self.examples) < original_count:
            self.save_examples()
            print(f"✅ 已删除训练示例: {name}")
        else:
            print(f"⚠️ 未找到训练示例: {name}")
    
    def list_examples(self, category: str = None):
        """列出训练示例"""
        examples = self.examples
        if category:
            examples = [ex for ex in examples if ex.category == category]
        
        print(f"\n📋 训练示例列表 ({len(examples)} 个):")
        print("-" * 80)
        
        for i, example in enumerate(examples, 1):
            print(f"{i:2d}. {example.name} [{example.category}]")
            print(f"    描述: {example.description}")
            print(f"    规则数: {len(example.expected_rules)}")
            print(f"    创建时间: {example.created_at}")
            print()
    
    def get_categories(self) -> List[str]:
        """获取所有类别"""
        categories = list(set(ex.category for ex in self.examples))
        return sorted(categories)
    
    def get_examples_by_category(self, category: str) -> List[TrainingExample]:
        """按类别获取示例"""
        return [ex for ex in self.examples if ex.category == category]
    
    def get_dspy_examples(self, categories: List[str] = None, max_count: int = None) -> List[dspy.Example]:
        """获取DSPy格式的训练示例"""
        examples = self.examples
        
        if categories:
            examples = [ex for ex in examples if ex.category in categories]
        
        if max_count:
            examples = examples[:max_count]
        
        return [ex.to_dspy_example() for ex in examples]
    
    def _create_default_examples(self):
        """创建默认的训练示例"""
        
        # 年龄+ECOG示例
        age_ecog_example = TrainingExample(
            name="年龄_ECOG评分",
            category="基础检查",
            description="年龄范围和ECOG评分的基本检查",
            clinical_text="""入选标准:
年龄18~70周岁，性别不限；
ECOG评分0-1分；
排除标准:
年龄不在18-70周岁范围内的患者；
ECOG评分>1分的患者；""",
            ecrf_info="""人口学资料\tDM\t出生日期\tBRTHDAT\t日期时间
ECOG评分\tRSECOG\tECOG评分分值\tRSORRES\t单选""",
            expected_rules=[
                {
                    "id": 1,
                    "tableName": "DM",
                    "checkItemOID": "DM.BRTHDAT",
                    "checkFieldOID": "BRTHDAT",
                    "checkItemName": "年龄",
                    "operator": "<",
                    "numericValue": 18,
                    "nonNumericValue": None,
                    "gender": None,
                    "unit": "周岁",
                    "message": "年龄<18周岁，不满足入选标准，请核实"
                },
                {
                    "id": 2,
                    "tableName": "DM",
                    "checkItemOID": "DM.BRTHDAT",
                    "checkFieldOID": "BRTHDAT",
                    "checkItemName": "年龄",
                    "operator": ">",
                    "numericValue": 70,
                    "nonNumericValue": None,
                    "gender": None,
                    "unit": "周岁",
                    "message": "年龄>70周岁，不满足入选标准，请核实"
                },
                {
                    "id": 3,
                    "tableName": "RSECOG",
                    "checkItemOID": "RSECOG.RSORRES",
                    "checkFieldOID": "RSORRES",
                    "checkItemName": "ECOG评分",
                    "operator": ">",
                    "numericValue": 1,
                    "nonNumericValue": None,
                    "gender": None,
                    "unit": None,
                    "message": "ECOG评分>1分，满足排除标准，请核实"
                }
            ]
        )
        
        # 实验室检查示例
        lab_example = TrainingExample(
            name="实验室检查_血常规",
            category="实验室检查",
            description="血常规相关的实验室检查标准",
            clinical_text="""入选标准:
骨髓功能基本正常：
ANC≥1.0×10^9/L；
血小板计数≥75×10^9/L；
排除标准:
ANC<1.0×10^9/L的患者；
血小板计数<75×10^9/L的患者；""",
            ecrf_info="""实验室检查明细\tLB_SUB\t检查项\tLBTEST\t文本
实验室检查明细\tLB_SUB\t结果\tLBORRES\t数值""",
            expected_rules=[
                {
                    "id": 1,
                    "tableName": "LB_SUB",
                    "checkItemOID": "LB_SUB.LBTEST",
                    "checkFieldOID": "LBORRES",
                    "checkItemName": "ANC",
                    "operator": "<",
                    "numericValue": 1.0,
                    "nonNumericValue": None,
                    "gender": None,
                    "unit": "10^9/L",
                    "message": "ANC<1.0×10^9/L，不满足入选标准，请核实"
                },
                {
                    "id": 2,
                    "tableName": "LB_SUB",
                    "checkItemOID": "LB_SUB.LBTEST",
                    "checkFieldOID": "LBORRES",
                    "checkItemName": "血小板计数",
                    "operator": "<",
                    "numericValue": 75,
                    "nonNumericValue": None,
                    "gender": None,
                    "unit": "10^9/L",
                    "message": "血小板计数<75×10^9/L，不满足入选标准，请核实"
                }
            ]
        )
        
        # 心电图示例
        ecg_example = TrainingExample(
            name="心电图_QTc间期",
            category="心电图检查",
            description="QTc间期的性别特异性检查",
            clinical_text="""排除标准:
心电图QTc间期延长（男性>450ms、女性>470ms）；""",
            ecrf_info="""12导联心电图明细\tEG_SUB\t检查项\tEGTEST\t文本
12导联心电图明细\tEG_SUB\t结果\tEGORRES\t数值""",
            expected_rules=[
                {
                    "id": 1,
                    "tableName": "EG_SUB",
                    "checkItemOID": "EG_SUB.EGTEST",
                    "checkFieldOID": "EGORRES",
                    "checkItemName": "QTc间期",
                    "operator": ">",
                    "numericValue": 450,
                    "nonNumericValue": None,
                    "gender": "Male",
                    "unit": "ms",
                    "message": "男性QTc间期>450ms，满足排除标准，请核实"
                },
                {
                    "id": 2,
                    "tableName": "EG_SUB",
                    "checkItemOID": "EG_SUB.EGTEST",
                    "checkFieldOID": "EGORRES",
                    "checkItemName": "QTc间期",
                    "operator": ">",
                    "numericValue": 470,
                    "nonNumericValue": None,
                    "gender": "Female",
                    "unit": "ms",
                    "message": "女性QTc间期>470ms，满足排除标准，请核实"
                }
            ]
        )
        
        # 添加默认示例
        self.examples = [age_ecog_example, lab_example, ecg_example]
        self.save_examples()


def interactive_training_manager():
    """交互式训练数据管理"""
    
    print("🎯 训练数据管理器")
    print("=" * 60)
    
    manager = TrainingManager()
    
    while True:
        print("\n请选择操作:")
        print("1. 查看所有示例")
        print("2. 按类别查看示例")
        print("3. 添加新示例")
        print("4. 删除示例")
        print("5. 查看类别列表")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            manager.list_examples()
        
        elif choice == "2":
            categories = manager.get_categories()
            print(f"\n可用类别: {', '.join(categories)}")
            category = input("请输入类别名称: ").strip()
            if category in categories:
                manager.list_examples(category)
            else:
                print("❌ 类别不存在")
        
        elif choice == "3":
            print("\n添加新的训练示例:")
            name = input("示例名称: ").strip()
            category = input("类别: ").strip()
            description = input("描述: ").strip()
            
            print("\n请输入临床文本 (输入空行结束):")
            clinical_lines = []
            while True:
                line = input()
                if line.strip() == "":
                    break
                clinical_lines.append(line)
            clinical_text = "\n".join(clinical_lines)
            
            print("\n请输入eCRF信息 (输入空行结束):")
            ecrf_lines = []
            while True:
                line = input()
                if line.strip() == "":
                    break
                ecrf_lines.append(line)
            ecrf_info = "\n".join(ecrf_lines)
            
            print("\n请输入期望规则的JSON格式 (输入空行结束):")
            rules_lines = []
            while True:
                line = input()
                if line.strip() == "":
                    break
                rules_lines.append(line)
            
            try:
                expected_rules = json.loads("\n".join(rules_lines))
                
                example = TrainingExample(
                    name=name,
                    category=category,
                    description=description,
                    clinical_text=clinical_text,
                    ecrf_info=ecrf_info,
                    expected_rules=expected_rules
                )
                
                manager.add_example(example)
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式错误: {e}")
        
        elif choice == "4":
            manager.list_examples()
            name = input("\n请输入要删除的示例名称: ").strip()
            manager.remove_example(name)
        
        elif choice == "5":
            categories = manager.get_categories()
            print(f"\n类别列表: {', '.join(categories)}")
        
        elif choice == "6":
            break
        
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    interactive_training_manager()
