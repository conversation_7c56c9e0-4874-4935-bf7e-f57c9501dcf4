[{"id": 1, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": "≥", "numericValue": 18, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄<18岁，不满足入选标准"}, {"id": 2, "tableName": "人口学资料", "checkItemOID": "DM", "checkFieldOID": "BRTHDAT", "checkItemName": "年龄", "operator": "≤", "numericValue": 70, "nonNumericValue": null, "gender": null, "unit": "岁", "message": "年龄>70岁，不满足入选标准"}, {"id": 3, "tableName": "ECOG评分", "checkItemOID": "RSECOG", "checkFieldOID": "RSORRES", "checkItemName": "ECOG评分", "operator": "between", "numericValue": [0, 1], "nonNumericValue": null, "gender": null, "unit": "分", "message": "ECOG评分不在0-1分范围内，不满足入选标准"}, {"id": 4, "tableName": "实验室检查明细", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LB_SUB.LBORRES", "checkItemName": "ANC", "operator": "≥", "numericValue": 1.0, "nonNumericValue": null, "gender": null, "unit": "×10^9/L", "message": "ANC<1.0×10^9/L，不满足入选标准"}, {"id": 5, "tableName": "实验室检查明细", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LB_SUB.LBORRES", "checkItemName": "血小板计数", "operator": "≥", "numericValue": 75, "nonNumericValue": null, "gender": null, "unit": "×10^9/L", "message": "血小板计数<75×10^9/L，不满足入选标准"}, {"id": 6, "tableName": "12导联心电图明细", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EG_SUB.EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 450, "nonNumericValue": null, "gender": "男", "unit": "ms", "message": "男性QTc间期>450ms，满足排除标准"}, {"id": 7, "tableName": "12导联心电图明细", "checkItemOID": "EGTEST", "checkFieldOID": "EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 470, "nonNumericValue": null, "gender": "女", "unit": "ms", "message": "女性QTc间期>470ms，满足排除标准"}, {"id": 8, "tableName": "病毒学定量检查明细", "checkItemOID": "LBVT_SUB.LBTEST", "checkFieldOID": "LBVT_SUB.LBORRES", "checkItemName": "HBV DNA", "operator": "≥", "numericValue": 2, "nonNumericValue": null, "gender": null, "unit": "×10³ IU/mL", "message": "HBV DNA≥2×10³ IU/mL，满足排除标准"}, {"id": 1, "tableName": "BIOMAR", "checkItemOID": "BMTEST", "checkFieldOID": "BMORRES", "checkItemName": "生物标志物ABC", "operator": "≥", "numericValue": 100, "nonNumericValue": null, "gender": null, "unit": "ng/mL", "message": "生物标志物ABC<100 ng/mL，不满足入选标准"}, {"id": 2, "tableName": "免疫学检查", "checkItemOID": "IMTEST", "checkFieldOID": "IMORRES", "checkItemName": "免疫指标XYZ", "operator": "=", "numericValue": null, "nonNumericValue": "阴性", "gender": null, "unit": null, "message": "免疫指标XYZ阳性，不满足入选标准"}, {"id": 3, "tableName": "SPECIAL", "checkItemOID": "SPTEST", "checkFieldOID": "SPORRES", "checkItemName": "实验性评分DEF", "operator": "≤", "numericValue": 5, "nonNumericValue": null, "gender": null, "unit": "分", "message": "实验性评分DEF>5分，不满足入选标准"}, {"id": 4, "tableName": "GENET", "checkItemOID": "GNTEST", "checkFieldOID": "GNORRES", "checkItemName": "遗传标记GHI", "operator": "=", "numericValue": null, "nonNumericValue": "阳性", "gender": null, "unit": null, "message": "遗传标记GHI阳性，满足排除标准"}, {"id": 5, "tableName": "生物标志物检查", "checkItemOID": "BMTEST", "checkFieldOID": "BMORRES", "checkItemName": "病毒JKL RNA", "operator": "≥", "numericValue": 1000, "nonNumericValue": null, "gender": null, "unit": "copies/mL", "message": "病毒JKL RNA≥1000 copies/mL，满足排除标准"}]