[{"id": 1, "tableName": "DM", "checkItemOID": "DM.BRTHDAT", "checkFieldOID": "DM.BRTHDAT", "checkItemName": "年龄", "operator": "BETWEEN", "numericValue": [18, 70], "nonNumericValue": null, "gender": null, "unit": "岁", "message": "受试者年龄必须在18-70周岁之间", "clinicalRationale": "确保受试者处于合适的生理状态"}, {"id": 2, "tableName": "RSECOG", "checkItemOID": "RSECOG.RSORRES", "checkFieldOID": "RSECOG.RSORRES", "checkItemName": "ECOG评分", "operator": "BETWEEN", "numericValue": [0, 1], "nonNumericValue": null, "gender": null, "unit": "分", "message": "ECOG评分必须在0-1分之间", "clinicalRationale": "评估患者体能状态，筛选身体状况较好的患者"}, {"id": 3, "tableName": "LB_SUB", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LB_SUB.LBORRES", "checkItemName": "中性粒细胞绝对值(ANC)", "operator": ">=", "numericValue": 1.0, "nonNumericValue": null, "gender": null, "unit": "×10^9/L", "message": "ANC必须≥1.0×10^9/L", "clinicalRationale": "确保患者有足够的造血功能，能耐受治疗"}, {"id": 4, "tableName": "LB_SUB", "checkItemOID": "LB_SUB.LBTEST", "checkFieldOID": "LB_SUB.LBORRES", "checkItemName": "血小板计数", "operator": ">=", "numericValue": 75, "nonNumericValue": null, "gender": null, "unit": "×10^9/L", "message": "血小板计数必须≥75×10^9/L", "clinicalRationale": "确保患者有足够的造血功能，能耐受治疗"}, {"id": 5, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EG_SUB.EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 450, "nonNumericValue": null, "gender": "Male", "unit": "ms", "message": "男性QTc间期>450ms需排除", "clinicalRationale": "排除有潜在心脏风险的男性患者"}, {"id": 6, "tableName": "EG_SUB", "checkItemOID": "EG_SUB.EGTEST", "checkFieldOID": "EG_SUB.EGORRES", "checkItemName": "QTc间期", "operator": ">", "numericValue": 470, "nonNumericValue": null, "gender": "Female", "unit": "ms", "message": "女性QTc间期>470ms需排除", "clinicalRationale": "排除有潜在心脏风险的女性患者"}, {"id": 7, "tableName": "LBVT_SUB", "checkItemOID": "LBVT_SUB.LBTEST", "checkFieldOID": "LBVT_SUB.LBORRES", "checkItemName": "HBV DNA载量", "operator": ">=", "numericValue": 2000, "nonNumericValue": null, "gender": null, "unit": "IU/mL", "message": "HBV DNA≥2×10³ IU/mL需排除", "clinicalRationale": "排除活动性乙肝病毒感染患者，降低治疗风险"}, {"id": 1, "tableName": "BIOMAR", "checkItemOID": "BIOMAR.TEST_NAME", "checkFieldOID": "BIOMAR.ORRES", "checkItemName": "ABC生物标志物", "operator": ">=", "numericValue": 100, "nonNumericValue": null, "gender": null, "unit": "ng/mL", "message": "ABC生物标志物水平必须≥100 ng/mL", "clinicalRationale": "筛选特定病理状态或治疗反应性患者"}, {"id": 2, "tableName": "IMMUNO", "checkItemOID": "IMMUNO.TEST_NAME", "checkFieldOID": "IMMUNO.ORRES", "checkItemName": "XYZ免疫指标", "operator": "=", "numericValue": null, "nonNumericValue": "阴性", "gender": null, "unit": null, "message": "XYZ免疫指标必须为阴性", "clinicalRationale": "排除自身免疫干扰"}, {"id": 3, "tableName": "SPECIAL", "checkItemOID": "SPECIAL.TEST_NAME", "checkFieldOID": "SPECIAL.ORRES", "checkItemName": "DEF实验性评分", "operator": "<=", "numericValue": 5, "nonNumericValue": null, "gender": null, "unit": null, "message": "DEF评分必须≤5分", "clinicalRationale": "确保患者病情严重度适中"}, {"id": 4, "tableName": "GENET", "checkItemOID": "GENET.TEST_NAME", "checkFieldOID": "GENET.ORRES", "checkItemName": "GHI遗传标记", "operator": "=", "numericValue": null, "nonNumericValue": "阳性", "gender": null, "unit": null, "message": "排除GHI遗传标记阳性患者", "clinicalRationale": "避免遗传因素干扰研究结果"}, {"id": 5, "tableName": "BIOMAR", "checkItemOID": "BIOMAR.TEST_NAME", "checkFieldOID": "BIOMAR.ORRES", "checkItemName": "JKL RNA病毒载量", "operator": ">=", "numericValue": 1000, "nonNumericValue": null, "gender": null, "unit": "copies/mL", "message": "排除JKL RNA≥1000 copies/mL患者", "clinicalRationale": "避免病毒感染影响研究结果"}]