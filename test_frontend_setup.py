"""
测试前端设置是否正确
"""

import os
import json
import subprocess
import sys

def check_frontend_setup():
    """检查前端设置"""
    
    print("🔍 检查前端项目设置...")
    
    # 检查目录结构
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print("❌ frontend目录不存在")
        return False
    
    # 检查package.json
    package_json_path = os.path.join(frontend_dir, "package.json")
    if not os.path.exists(package_json_path):
        print("❌ package.json不存在")
        return False
    
    # 读取package.json
    try:
        with open(package_json_path, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        print(f"✅ 项目名称: {package_data.get('name', 'N/A')}")
        print(f"✅ 项目版本: {package_data.get('version', 'N/A')}")
    except Exception as e:
        print(f"❌ 读取package.json失败: {e}")
        return False
    
    # 检查关键文件
    key_files = [
        "vite.config.ts",
        "tsconfig.json", 
        "tailwind.config.js",
        "index.html",
        "src/main.tsx",
        "src/App.tsx",
        "src/index.css"
    ]
    
    for file_path in key_files:
        full_path = os.path.join(frontend_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 缺失")
            return False
    
    # 检查源码目录结构
    src_dirs = [
        "src/components",
        "src/pages", 
        "src/services",
        "src/hooks",
        "src/contexts",
        "src/types"
    ]
    
    for dir_path in src_dirs:
        full_path = os.path.join(frontend_dir, dir_path)
        if os.path.exists(full_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ 缺失")
            return False
    
    print("✅ 前端项目结构检查通过")
    return True

def check_node_environment():
    """检查Node.js环境"""
    
    print("\n🔍 检查Node.js环境...")
    
    try:
        # 检查Node.js版本
        node_result = subprocess.run(['node', '--version'], 
                                   capture_output=True, text=True)
        if node_result.returncode == 0:
            node_version = node_result.stdout.strip()
            print(f"✅ Node.js版本: {node_version}")
            
            # 检查版本是否满足要求 (>= 16.0.0)
            version_num = node_version.replace('v', '').split('.')[0]
            if int(version_num) >= 16:
                print("✅ Node.js版本满足要求")
            else:
                print("⚠️ Node.js版本过低，建议升级到16+")
        else:
            print("❌ Node.js未安装或不在PATH中")
            return False
            
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False
    
    try:
        # 检查npm版本
        npm_result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True)
        if npm_result.returncode == 0:
            npm_version = npm_result.stdout.strip()
            print(f"✅ npm版本: {npm_version}")
        else:
            print("❌ npm不可用")
            return False
            
    except FileNotFoundError:
        print("❌ npm未安装")
        return False
    
    return True

def install_dependencies():
    """安装前端依赖"""
    
    print("\n📦 安装前端依赖...")
    
    try:
        os.chdir("frontend")
        
        # 检查是否已安装依赖
        if os.path.exists("node_modules"):
            print("✅ node_modules已存在，跳过安装")
            return True
        
        # 安装依赖
        print("📥 正在安装依赖包...")
        result = subprocess.run(['npm', 'install'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False
    finally:
        os.chdir("..")

def main():
    """主函数"""
    
    print("🎯 前端项目设置检查")
    print("=" * 50)
    
    # 检查项目结构
    if not check_frontend_setup():
        print("\n❌ 前端项目设置检查失败")
        return False
    
    # 检查Node.js环境
    if not check_node_environment():
        print("\n❌ Node.js环境检查失败")
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 前端项目设置检查完成！")
    print("\n📋 下一步操作:")
    print("1. cd frontend")
    print("2. npm run dev")
    print("3. 访问 http://localhost:3000")
    print("\n或者直接运行: start_frontend.bat")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
